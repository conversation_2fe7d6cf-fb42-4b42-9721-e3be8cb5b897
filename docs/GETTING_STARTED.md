# Getting Started

> **🏎️ Complete setup and development guide for ADHD Trading Dashboard**

## 🚀 Quick Start

### Prerequisites

- **Node.js v18 LTS** (recommended)
- **Yarn** package manager
- **Git**

### Setup

```bash
# Clone and install
git clone https://github.com/HeinekenBottle/adhd-trading-dashboard-lib.git
cd adhd-trading-dashboard-lib
yarn install

# Start development
yarn dev
```

App runs at [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
📦 adhd-trading-dashboard-lib/
├── 📁 packages/
│   ├── 📁 shared/          # Reusable components, theme, utilities
│   └── 📁 dashboard/       # Main trading application
├── 📁 docs/               # Documentation
├── 📁 scripts/            # Development tools
└── 📄 package.json        # Root workspace
```

**Dependency Flow**: `shared → dashboard`

## 🛠️ Commands

```bash
# Development
yarn dev                    # Start dev server
yarn build                  # Build for production
yarn test                   # Run tests

# Code Quality
yarn lint                   # Lint code
yarn health                 # System health check
yarn analyze                # Architecture analysis

# Dependencies
yarn deps:check             # Check versions
yarn deps:sync              # Sync versions
```

## � Development Workflow

### Adding Components

**Shared Components** (reusable):

```
packages/shared/src/components/
├── atoms/          # Basic elements (Button, Input)
├── molecules/      # Simple combos (FormField, Card)
└── organisms/      # Complex components (DataTable, Modal)
```

**Feature Components** (specific):

```
packages/dashboard/src/features/your-feature/
├── components/     # Feature components
├── hooks/          # Feature hooks
└── types/          # Feature types
```

### Theme Usage

```typescript
import { useTheme } from 'styled-components';

const MyComponent = () => {
  const theme = useTheme();
  return (
    <div
      style={{
        color: theme.colors.primary,
        background: theme.colors.background.dark,
      }}
    >
      F1-themed content
    </div>
  );
};
```

## 🔍 Troubleshooting

### Common Setup Issues

```bash
# Clear yarn cache
yarn cache clean

# Remove node_modules and reinstall
rm -rf node_modules packages/*/node_modules
```

**Issue: TypeScript errors during build**

```bash
# Clear TypeScript cache
rm -rf packages/*/tsconfig.tsbuildinfo

# Rebuild with clean slate
yarn clean && yarn build
```

**Issue: Port 3000 already in use**

```bash
# Kill process using port 3000
lsof -ti:3000 | xargs kill -9

# Or start on different port
yarn dev --port 3001
```

**Issue: Storybook won't start**

```bash
# Clear Storybook cache
yarn storybook --no-manager-cache

# Or rebuild Storybook
rm -rf packages/shared/.storybook-static
yarn build-storybook
```

### Getting Help

If you encounter issues:

1. **Check the logs** - Look for error messages in the terminal
2. **Verify prerequisites** - Ensure Node.js v18 and Yarn are installed
3. **Check documentation** - Browse the [docs/](../README.md) directory
4. **Create an issue** - If problems persist, create a GitHub issue

## 📚 Next Steps

Now that you have the project running, explore these resources:

### Learn the Architecture

- **[System Architecture](./ARCHITECTURE.md)** - Understand the monorepo structure
- **[Shared Package](./packages/SHARED.md)** - Learn about reusable components
- **[Dashboard Package](./packages/DASHBOARD.md)** - Explore trading features

### Development Guides

- **[Development Workflow](./DEVELOPMENT.md)** - Best practices and workflows
- **[Build System](./technical/BUILD.md)** - Build processes and deployment
- **[API Reference](./API_REFERENCE.md)** - Complete API documentation

### Component Development

- **[Theme System](./THEME_SYSTEM.md)** - Design tokens and styling
- **Storybook** - Run `yarn storybook` to explore components
- **Testing** - Learn about our testing strategy

## 🏁 Ready to Race!

You're now ready to start developing with the ADHD Trading Dashboard! The Formula 1-inspired interface is designed for high performance, just like the development experience.

**Happy coding!** 🏎️💨

---

**Need help?** Check the [Development Guide](./DEVELOPMENT.md) or create an issue on GitHub.
