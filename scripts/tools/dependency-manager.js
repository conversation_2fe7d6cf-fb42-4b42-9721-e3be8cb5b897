#!/usr/bin/env node

/**
 * Dependency Management Tool
 *
 * Comprehensive tool for managing dependencies across the monorepo:
 * - Version consistency checking
 * - Dependency updates
 * - Security auditing
 * - Workspace optimization
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';
import chalk from 'chalk';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Configuration
 */
const CONFIG = {
  rootDir: path.resolve(__dirname, '../..'),
  packages: [
    { name: 'root', path: '.' },
    { name: 'shared', path: 'packages/shared' },
    { name: 'dashboard', path: 'packages/dashboard' },
  ],
  criticalDependencies: ['react', 'react-dom', 'typescript', 'styled-components'],
};

/**
 * Read package.json from a path
 */
function readPackageJson(packagePath) {
  const fullPath = path.join(CONFIG.rootDir, packagePath, 'package.json');
  if (!fs.existsSync(fullPath)) {
    return null;
  }

  try {
    return JSON.parse(fs.readFileSync(fullPath, 'utf8'));
  } catch (error) {
    console.warn(chalk.yellow(`⚠️  Could not read ${fullPath}: ${error.message}`));
    return null;
  }
}

/**
 * Get all dependencies from a package.json
 */
function getAllDependencies(packageJson) {
  if (!packageJson) return {};

  return {
    ...packageJson.dependencies,
    ...packageJson.devDependencies,
    ...packageJson.peerDependencies,
  };
}

/**
 * Check version consistency across packages
 */
function checkVersionConsistency() {
  console.log(chalk.blue('🔍 Checking dependency version consistency...\n'));

  const dependencyVersions = new Map();
  const inconsistencies = [];

  // Collect all dependencies and their versions
  for (const pkg of CONFIG.packages) {
    const packageJson = readPackageJson(pkg.path);
    if (!packageJson) continue;

    const dependencies = getAllDependencies(packageJson);

    for (const [depName, version] of Object.entries(dependencies)) {
      if (!dependencyVersions.has(depName)) {
        dependencyVersions.set(depName, new Map());
      }

      const versionMap = dependencyVersions.get(depName);
      if (!versionMap.has(version)) {
        versionMap.set(version, []);
      }

      versionMap.get(version).push(pkg.name);
    }
  }

  // Find inconsistencies
  for (const [depName, versionMap] of dependencyVersions) {
    if (versionMap.size > 1) {
      inconsistencies.push({
        dependency: depName,
        versions: Array.from(versionMap.entries()).map(([version, packages]) => ({
          version,
          packages,
        })),
      });
    }
  }

  // Report results
  if (inconsistencies.length === 0) {
    console.log(chalk.green('✅ All dependencies have consistent versions!'));
  } else {
    console.log(chalk.red(`❌ Found ${inconsistencies.length} version inconsistencies:\n`));

    for (const inconsistency of inconsistencies) {
      console.log(chalk.yellow(`📦 ${inconsistency.dependency}:`));
      for (const { version, packages } of inconsistency.versions) {
        console.log(chalk.gray(`   ${version} → ${packages.join(', ')}`));
      }
      console.log('');
    }
  }

  return inconsistencies;
}

/**
 * Check for outdated dependencies
 */
function checkOutdatedDependencies() {
  console.log(chalk.blue('📅 Checking for outdated dependencies...\n'));

  try {
    console.log(chalk.gray('Running yarn outdated...'));
    const output = execSync('yarn outdated --json', {
      cwd: CONFIG.rootDir,
      encoding: 'utf8',
    });

    const lines = output.trim().split('\n');
    const outdatedPackages = [];

    for (const line of lines) {
      try {
        const data = JSON.parse(line);
        if (data.type === 'table' && data.data && data.data.body) {
          outdatedPackages.push(...data.data.body);
        }
      } catch (e) {
        // Ignore non-JSON lines
      }
    }

    if (outdatedPackages.length === 0) {
      console.log(chalk.green('✅ All dependencies are up to date!'));
    } else {
      console.log(chalk.yellow(`📦 Found ${outdatedPackages.length} outdated dependencies:`));

      for (const pkg of outdatedPackages) {
        const [name, current, wanted, latest] = pkg;
        console.log(chalk.gray(`   ${name}: ${current} → ${wanted} (latest: ${latest})`));
      }
    }

    return outdatedPackages;
  } catch (error) {
    console.log(chalk.gray('ℹ️  Could not check outdated dependencies (this is normal)'));
    return [];
  }
}

/**
 * Run security audit
 */
function runSecurityAudit() {
  console.log(chalk.blue('🔒 Running security audit...\n'));

  try {
    console.log(chalk.gray('Running yarn audit...'));
    execSync('yarn audit --json', {
      cwd: CONFIG.rootDir,
      stdio: 'inherit',
    });

    console.log(chalk.green('✅ No security vulnerabilities found!'));
  } catch (error) {
    console.log(chalk.yellow('⚠️  Security audit found issues. Review the output above.'));
  }
}

/**
 * Synchronize dependency versions across packages
 */
function synchronizeDependencies() {
  console.log(chalk.blue('🔄 Synchronizing dependency versions...\n'));

  const inconsistencies = checkVersionConsistency();
  if (inconsistencies.length === 0) {
    console.log(chalk.green('✅ All dependencies are already synchronized!'));
    return;
  }

  console.log(chalk.yellow(`📦 Synchronizing ${inconsistencies.length} dependencies...\n`));

  for (const inconsistency of inconsistencies) {
    const { dependency, versions } = inconsistency;

    // Find the most recent version
    const latestVersion = versions.reduce((latest, current) => {
      // Remove caret/tilde prefixes for comparison
      const cleanLatest = latest.version.replace(/^[\^~]/, '');
      const cleanCurrent = current.version.replace(/^[\^~]/, '');

      // Simple version comparison (could be enhanced with semver)
      return cleanCurrent > cleanLatest ? current : latest;
    });

    console.log(chalk.blue(`📦 ${dependency}: Using version ${latestVersion.version}`));

    // Update package.json files
    for (const { version, packages } of versions) {
      if (version !== latestVersion.version) {
        for (const packageName of packages) {
          updateDependencyVersion(packageName, dependency, latestVersion.version);
        }
      }
    }
  }

  console.log(chalk.green('\n✅ Dependencies synchronized!'));
  console.log(chalk.yellow('💡 Run "yarn install" to apply changes'));
}

/**
 * Update dependency version in a specific package
 */
function updateDependencyVersion(packageName, dependency, newVersion) {
  const packagePath = packageName === 'root' ? '.' : `packages/${packageName}`;
  const packageJsonPath = path.join(CONFIG.rootDir, packagePath, 'package.json');

  if (!fs.existsSync(packageJsonPath)) {
    console.warn(chalk.yellow(`⚠️  Package.json not found: ${packageJsonPath}`));
    return;
  }

  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    let updated = false;

    // Update in dependencies
    if (packageJson.dependencies && packageJson.dependencies[dependency]) {
      packageJson.dependencies[dependency] = newVersion;
      updated = true;
    }

    // Update in devDependencies
    if (packageJson.devDependencies && packageJson.devDependencies[dependency]) {
      packageJson.devDependencies[dependency] = newVersion;
      updated = true;
    }

    // Update in peerDependencies
    if (packageJson.peerDependencies && packageJson.peerDependencies[dependency]) {
      packageJson.peerDependencies[dependency] = newVersion;
      updated = true;
    }

    if (updated) {
      fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
      console.log(chalk.gray(`   Updated ${packageName}: ${dependency}@${newVersion}`));
    }
  } catch (error) {
    console.error(chalk.red(`❌ Failed to update ${packageName}: ${error.message}`));
  }
}

/**
 * Optimize workspace dependencies
 */
function optimizeWorkspace() {
  console.log(chalk.blue('⚡ Optimizing workspace dependencies...\n'));

  try {
    // First synchronize versions
    synchronizeDependencies();

    // Clean node_modules
    console.log(chalk.gray('Cleaning node_modules...'));
    execSync('rm -rf node_modules packages/*/node_modules', {
      cwd: CONFIG.rootDir,
    });

    // Reinstall with optimization
    console.log(chalk.gray('Reinstalling dependencies...'));
    execSync('yarn install --frozen-lockfile', {
      cwd: CONFIG.rootDir,
      stdio: 'inherit',
    });

    console.log(chalk.green('✅ Workspace optimized!'));
  } catch (error) {
    console.error(chalk.red(`❌ Optimization failed: ${error.message}`));
  }
}

/**
 * Generate dependency report
 */
function generateReport() {
  console.log(chalk.blue('📊 Generating dependency report...\n'));

  const report = {
    timestamp: new Date().toISOString(),
    packages: {},
    summary: {
      totalDependencies: 0,
      uniqueDependencies: 0,
      inconsistencies: 0,
    },
  };

  const allDependencies = new Set();

  // Collect package information
  for (const pkg of CONFIG.packages) {
    const packageJson = readPackageJson(pkg.path);
    if (!packageJson) continue;

    const dependencies = getAllDependencies(packageJson);
    const depCount = Object.keys(dependencies).length;

    report.packages[pkg.name] = {
      version: packageJson.version,
      dependencyCount: depCount,
      dependencies: dependencies,
    };

    report.summary.totalDependencies += depCount;

    for (const depName of Object.keys(dependencies)) {
      allDependencies.add(depName);
    }
  }

  report.summary.uniqueDependencies = allDependencies.size;

  // Check inconsistencies
  const inconsistencies = checkVersionConsistency();
  report.summary.inconsistencies = inconsistencies.length;

  // Write report
  const reportPath = path.join(CONFIG.rootDir, 'dependency-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

  console.log(chalk.green(`📄 Report saved to: ${reportPath}`));
  console.log(chalk.gray(`   Total dependencies: ${report.summary.totalDependencies}`));
  console.log(chalk.gray(`   Unique dependencies: ${report.summary.uniqueDependencies}`));
  console.log(chalk.gray(`   Version inconsistencies: ${report.summary.inconsistencies}`));

  return report;
}

/**
 * CLI interface
 */
const command = process.argv[2];

switch (command) {
  case 'check':
    checkVersionConsistency();
    break;
  case 'sync':
    synchronizeDependencies();
    break;
  case 'outdated':
    checkOutdatedDependencies();
    break;
  case 'audit':
    runSecurityAudit();
    break;
  case 'optimize':
    optimizeWorkspace();
    break;
  case 'report':
    generateReport();
    break;
  case 'all':
    checkVersionConsistency();
    checkOutdatedDependencies();
    runSecurityAudit();
    generateReport();
    break;
  default:
    console.log(chalk.blue('Dependency Management Tool\n'));
    console.log('Usage:');
    console.log('  node dependency-manager.js check     - Check version consistency');
    console.log('  node dependency-manager.js sync      - Synchronize dependency versions');
    console.log('  node dependency-manager.js outdated  - Check for outdated packages');
    console.log('  node dependency-manager.js audit     - Run security audit');
    console.log('  node dependency-manager.js optimize  - Optimize workspace');
    console.log('  node dependency-manager.js report    - Generate dependency report');
    console.log('  node dependency-manager.js all       - Run all checks');
    break;
}

export {
  checkVersionConsistency,
  synchronizeDependencies,
  checkOutdatedDependencies,
  runSecurityAudit,
  optimizeWorkspace,
  generateReport,
};
