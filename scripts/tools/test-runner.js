#!/usr/bin/env node

/**
 * Enhanced Test Runner
 * 
 * Comprehensive testing workflow tool with:
 * - Unit tests (Vitest)
 * - Component tests (React Testing Library)
 * - E2E tests (Playwright)
 * - Coverage reporting
 * - Performance testing
 */

import { spawn, execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import chalk from 'chalk';
import { performance } from 'perf_hooks';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Test configuration
 */
const TEST_CONFIG = {
  rootDir: path.resolve(__dirname, '../..'),
  packages: ['shared', 'dashboard'],
  testTypes: {
    unit: {
      command: 'yarn test',
      description: 'Unit tests with Vitest'
    },
    component: {
      command: 'yarn test:component',
      description: 'Component tests with React Testing Library'
    },
    e2e: {
      command: 'yarn test:e2e',
      description: 'End-to-end tests with Playwright'
    },
    coverage: {
      command: 'yarn test:coverage',
      description: 'Test coverage report'
    }
  }
};

/**
 * Enhanced process runner with timing
 */
function runCommand(name, command, cwd = TEST_CONFIG.rootDir) {
  const startTime = performance.now();
  
  console.log(chalk.blue(`🧪 Running ${name}...`));
  console.log(chalk.gray(`   Command: ${command}`));
  console.log(chalk.gray(`   Working directory: ${cwd}\n`));

  try {
    execSync(command, {
      cwd,
      stdio: 'inherit',
      env: { ...process.env, FORCE_COLOR: 'true' }
    });

    const duration = performance.now() - startTime;
    console.log(chalk.green(`\n✅ ${name} completed in ${(duration / 1000).toFixed(2)}s\n`));
    return true;
  } catch (error) {
    const duration = performance.now() - startTime;
    console.error(chalk.red(`\n❌ ${name} failed after ${(duration / 1000).toFixed(2)}s`));
    console.error(chalk.red(`   Exit code: ${error.status}\n`));
    return false;
  }
}

/**
 * Run unit tests
 */
function runUnitTests(packageName = null) {
  console.log(chalk.blue('🔬 Running Unit Tests\n'));

  if (packageName) {
    const packagePath = path.join(TEST_CONFIG.rootDir, 'packages', packageName);
    return runCommand(`Unit Tests (${packageName})`, 'yarn test', packagePath);
  } else {
    // Run tests for all packages
    let allPassed = true;
    
    for (const pkg of TEST_CONFIG.packages) {
      const packagePath = path.join(TEST_CONFIG.rootDir, 'packages', pkg);
      if (fs.existsSync(packagePath)) {
        const passed = runCommand(`Unit Tests (${pkg})`, 'yarn test', packagePath);
        allPassed = allPassed && passed;
      }
    }
    
    return allPassed;
  }
}

/**
 * Run component tests
 */
function runComponentTests() {
  console.log(chalk.blue('🧩 Running Component Tests\n'));
  
  // Component tests are typically in the dashboard package
  const dashboardPath = path.join(TEST_CONFIG.rootDir, 'packages/dashboard');
  return runCommand('Component Tests', 'yarn test --run src/**/*.test.tsx', dashboardPath);
}

/**
 * Run E2E tests
 */
function runE2ETests() {
  console.log(chalk.blue('🌐 Running E2E Tests\n'));
  
  // First, ensure the application is built
  console.log(chalk.yellow('📦 Building application for E2E tests...'));
  const buildSuccess = runCommand('Build for E2E', 'yarn build');
  
  if (!buildSuccess) {
    console.error(chalk.red('❌ Build failed, skipping E2E tests'));
    return false;
  }

  return runCommand('E2E Tests', 'yarn test:e2e');
}

/**
 * Generate coverage report
 */
function generateCoverage() {
  console.log(chalk.blue('📊 Generating Coverage Report\n'));
  
  return runCommand('Coverage Report', 'yarn test:coverage');
}

/**
 * Run performance tests
 */
function runPerformanceTests() {
  console.log(chalk.blue('⚡ Running Performance Tests\n'));
  
  // Performance tests using Playwright
  return runCommand('Performance Tests', 'yarn test:e2e --grep="performance"');
}

/**
 * Validate test setup
 */
function validateTestSetup() {
  console.log(chalk.blue('🔍 Validating Test Setup\n'));

  const issues = [];

  // Check for test configuration files
  const requiredFiles = [
    'vitest.config.ts',
    'playwright.config.ts',
    'vitest.setup.ts'
  ];

  for (const file of requiredFiles) {
    const filePath = path.join(TEST_CONFIG.rootDir, file);
    if (!fs.existsSync(filePath)) {
      issues.push(`Missing test config: ${file}`);
    } else {
      console.log(chalk.green(`✅ Found: ${file}`));
    }
  }

  // Check for test directories
  const testDirs = [
    'tests',
    'packages/shared/src',
    'packages/dashboard/src'
  ];

  for (const dir of testDirs) {
    const dirPath = path.join(TEST_CONFIG.rootDir, dir);
    if (fs.existsSync(dirPath)) {
      console.log(chalk.green(`✅ Test directory: ${dir}`));
    } else {
      console.log(chalk.yellow(`⚠️  Missing test directory: ${dir}`));
    }
  }

  // Check for test dependencies
  const packageJsonPath = path.join(TEST_CONFIG.rootDir, 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const devDeps = packageJson.devDependencies || {};

  const requiredTestDeps = [
    'vitest',
    '@playwright/test',
    '@testing-library/react',
    '@testing-library/jest-dom'
  ];

  for (const dep of requiredTestDeps) {
    if (devDeps[dep]) {
      console.log(chalk.green(`✅ Test dependency: ${dep}`));
    } else {
      issues.push(`Missing test dependency: ${dep}`);
    }
  }

  if (issues.length === 0) {
    console.log(chalk.green('\n🎉 Test setup is valid!'));
  } else {
    console.log(chalk.red('\n❌ Test setup issues:'));
    issues.forEach(issue => console.log(chalk.red(`   - ${issue}`)));
  }

  return issues.length === 0;
}

/**
 * Run comprehensive test suite
 */
function runFullTestSuite() {
  console.log(chalk.blue('🚀 Running Full Test Suite\n'));
  
  const results = {
    validation: false,
    unit: false,
    component: false,
    e2e: false,
    coverage: false
  };

  // 1. Validate setup
  console.log(chalk.yellow('1. Validating test setup...'));
  results.validation = validateTestSetup();
  
  if (!results.validation) {
    console.error(chalk.red('❌ Test setup validation failed, aborting'));
    return results;
  }

  // 2. Unit tests
  console.log(chalk.yellow('2. Running unit tests...'));
  results.unit = runUnitTests();

  // 3. Component tests
  console.log(chalk.yellow('3. Running component tests...'));
  results.component = runComponentTests();

  // 4. E2E tests
  console.log(chalk.yellow('4. Running E2E tests...'));
  results.e2e = runE2ETests();

  // 5. Coverage report
  console.log(chalk.yellow('5. Generating coverage report...'));
  results.coverage = generateCoverage();

  // Summary
  console.log(chalk.blue('\n📋 Test Suite Summary:'));
  console.log(chalk.blue('========================'));
  
  const testResults = [
    ['Validation', results.validation],
    ['Unit Tests', results.unit],
    ['Component Tests', results.component],
    ['E2E Tests', results.e2e],
    ['Coverage', results.coverage]
  ];

  for (const [name, passed] of testResults) {
    const status = passed ? chalk.green('✅ PASS') : chalk.red('❌ FAIL');
    console.log(`${status} ${name}`);
  }

  const allPassed = Object.values(results).every(Boolean);
  
  if (allPassed) {
    console.log(chalk.green('\n🎉 All tests passed!'));
  } else {
    console.log(chalk.red('\n💥 Some tests failed'));
  }

  return results;
}

/**
 * CLI interface
 */
const command = process.argv[2];
const packageName = process.argv[3];

switch (command) {
  case 'unit':
    runUnitTests(packageName);
    break;
  case 'component':
    runComponentTests();
    break;
  case 'e2e':
    runE2ETests();
    break;
  case 'coverage':
    generateCoverage();
    break;
  case 'performance':
    runPerformanceTests();
    break;
  case 'validate':
    validateTestSetup();
    break;
  case 'all':
    runFullTestSuite();
    break;
  default:
    console.log(chalk.blue('Enhanced Test Runner\n'));
    console.log('Usage:');
    console.log('  node test-runner.js unit [package]  - Run unit tests');
    console.log('  node test-runner.js component       - Run component tests');
    console.log('  node test-runner.js e2e             - Run E2E tests');
    console.log('  node test-runner.js coverage        - Generate coverage report');
    console.log('  node test-runner.js performance     - Run performance tests');
    console.log('  node test-runner.js validate        - Validate test setup');
    console.log('  node test-runner.js all             - Run full test suite');
    break;
}

export { 
  runUnitTests, 
  runComponentTests, 
  runE2ETests, 
  generateCoverage, 
  runPerformanceTests,
  validateTestSetup,
  runFullTestSuite 
};
