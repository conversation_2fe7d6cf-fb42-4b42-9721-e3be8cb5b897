#!/usr/bin/env node

/**
 * TypeScript Configuration Optimizer
 * 
 * Optimizes TypeScript configurations across the monorepo for:
 * - ES Module compatibility
 * - Composite project setup
 * - Consistent compiler options
 * - Build performance
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import chalk from 'chalk';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Optimal TypeScript configurations
const OPTIMAL_ROOT_CONFIG = {
  compilerOptions: {
    target: "ES2022",
    lib: ["ES2022", "DOM", "DOM.Iterable"],
    module: "ESNext",
    moduleResolution: "Node",
    allowJs: true,
    skipLibCheck: true,
    esModuleInterop: true,
    allowSyntheticDefaultImports: true,
    strict: true,
    forceConsistentCasingInFileNames: true,
    noFallthroughCasesInSwitch: true,
    noUnusedLocals: true,
    noUnusedParameters: true,
    noImplicitReturns: true,
    noImplicitAny: true,
    noImplicitThis: true,
    strictNullChecks: true,
    resolveJsonModule: true,
    jsx: "react-jsx",
    baseUrl: ".",
    paths: {
      "@adhd-trading-dashboard/shared": ["packages/shared/src"],
      "@adhd-trading-dashboard/shared/*": ["packages/shared/src/*"],
      "@adhd-trading-dashboard/dashboard": ["packages/dashboard/src"],
      "@adhd-trading-dashboard/dashboard/*": ["packages/dashboard/src/*"]
    },
    composite: true,
    declaration: true,
    declarationMap: true,
    sourceMap: true,
    isolatedModules: true,
    incremental: true,
    tsBuildInfoFile: ".tsbuildinfo"
  },
  exclude: ["node_modules", "dist", "scripts", "**/node_modules", "**/dist"],
  references: [
    { path: "./packages/shared" },
    { path: "./packages/dashboard" }
  ]
};

const OPTIMAL_SHARED_CONFIG = {
  extends: "../../tsconfig.json",
  compilerOptions: {
    outDir: "dist",
    rootDir: "src",
    composite: true,
    declaration: true,
    declarationMap: true,
    sourceMap: true,
    jsx: "react-jsx",
    esModuleInterop: true,
    allowSyntheticDefaultImports: true,
    tsBuildInfoFile: "tsconfig.tsbuildinfo"
  },
  include: ["src/**/*"],
  exclude: ["node_modules", "dist", "**/*.test.*", "**/*.spec.*"]
};

const OPTIMAL_DASHBOARD_CONFIG = {
  extends: "../../tsconfig.json",
  compilerOptions: {
    outDir: "dist",
    rootDir: "src",
    composite: true,
    declaration: true,
    declarationMap: true,
    sourceMap: true,
    jsx: "react-jsx",
    esModuleInterop: true,
    allowSyntheticDefaultImports: true,
    tsBuildInfoFile: "tsconfig.tsbuildinfo"
  },
  include: ["src/**/*"],
  exclude: ["node_modules", "dist", "**/*.test.*", "**/*.spec.*"],
  references: [
    { path: "../shared" }
  ]
};

/**
 * Backup existing configuration
 */
function backupConfig(configPath) {
  if (fs.existsSync(configPath)) {
    const backupPath = `${configPath}.backup.${Date.now()}`;
    fs.copyFileSync(configPath, backupPath);
    console.log(chalk.gray(`   Backed up to: ${backupPath}`));
  }
}

/**
 * Write optimized configuration
 */
function writeConfig(configPath, config) {
  const content = JSON.stringify(config, null, 2);
  fs.writeFileSync(configPath, content);
  console.log(chalk.green(`✅ Updated: ${configPath}`));
}

/**
 * Optimize TypeScript configurations
 */
function optimizeTypeScriptConfigs() {
  console.log(chalk.blue('🔧 Optimizing TypeScript Configurations...\n'));

  const rootDir = path.resolve(__dirname, '../..');
  
  // 1. Root tsconfig.json
  console.log(chalk.yellow('📝 Updating root tsconfig.json...'));
  const rootConfigPath = path.join(rootDir, 'tsconfig.json');
  backupConfig(rootConfigPath);
  writeConfig(rootConfigPath, OPTIMAL_ROOT_CONFIG);

  // 2. Shared package tsconfig.json
  console.log(chalk.yellow('📝 Updating shared package tsconfig.json...'));
  const sharedConfigPath = path.join(rootDir, 'packages/shared/tsconfig.json');
  backupConfig(sharedConfigPath);
  writeConfig(sharedConfigPath, OPTIMAL_SHARED_CONFIG);

  // 3. Dashboard package tsconfig.json
  console.log(chalk.yellow('📝 Updating dashboard package tsconfig.json...'));
  const dashboardConfigPath = path.join(rootDir, 'packages/dashboard/tsconfig.json');
  backupConfig(dashboardConfigPath);
  writeConfig(dashboardConfigPath, OPTIMAL_DASHBOARD_CONFIG);

  console.log(chalk.green('\n🎉 TypeScript configurations optimized!'));
  console.log(chalk.gray('   Benefits:'));
  console.log(chalk.gray('   - ES2022 target for modern JavaScript features'));
  console.log(chalk.gray('   - Proper composite project setup'));
  console.log(chalk.gray('   - Incremental compilation support'));
  console.log(chalk.gray('   - Consistent module resolution'));
  console.log(chalk.gray('   - Enhanced error checking'));
}

/**
 * Validate TypeScript setup
 */
function validateTypeScriptSetup() {
  console.log(chalk.blue('\n🔍 Validating TypeScript Setup...\n'));

  const rootDir = path.resolve(__dirname, '../..');
  const issues = [];

  // Check for required files
  const requiredFiles = [
    'tsconfig.json',
    'packages/shared/tsconfig.json',
    'packages/dashboard/tsconfig.json'
  ];

  for (const file of requiredFiles) {
    const filePath = path.join(rootDir, file);
    if (!fs.existsSync(filePath)) {
      issues.push(`Missing: ${file}`);
    }
  }

  // Check for old build artifacts
  const buildArtifacts = [
    'packages/shared/tsconfig.tsbuildinfo',
    'packages/dashboard/tsconfig.tsbuildinfo',
    '.tsbuildinfo'
  ];

  for (const artifact of buildArtifacts) {
    const artifactPath = path.join(rootDir, artifact);
    if (fs.existsSync(artifactPath)) {
      console.log(chalk.gray(`   Found build cache: ${artifact}`));
    }
  }

  if (issues.length === 0) {
    console.log(chalk.green('✅ TypeScript setup is valid!'));
  } else {
    console.log(chalk.red('❌ TypeScript setup issues:'));
    issues.forEach(issue => console.log(chalk.red(`   - ${issue}`)));
  }

  return issues.length === 0;
}

// CLI interface
const command = process.argv[2];

switch (command) {
  case 'optimize':
    optimizeTypeScriptConfigs();
    break;
  case 'validate':
    validateTypeScriptSetup();
    break;
  case 'all':
    optimizeTypeScriptConfigs();
    validateTypeScriptSetup();
    break;
  default:
    console.log(chalk.blue('TypeScript Configuration Optimizer\n'));
    console.log('Usage:');
    console.log('  node typescript-optimizer.js optimize  - Optimize all TypeScript configs');
    console.log('  node typescript-optimizer.js validate  - Validate TypeScript setup');
    console.log('  node typescript-optimizer.js all       - Optimize and validate');
    break;
}

export { optimizeTypeScriptConfigs, validateTypeScriptSetup };
