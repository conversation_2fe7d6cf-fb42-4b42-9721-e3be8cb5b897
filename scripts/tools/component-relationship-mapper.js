#!/usr/bin/env node

/**
 * Component Relationship Mapper for ADHD Trading Dashboard
 *
 * Maps component relationships and usage patterns:
 * 1. Parent-child component relationships
 * 2. Shared component usage across features
 * 3. Atomic design hierarchy validation
 * 4. Component reusability analysis
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import chalk from 'chalk';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const DASHBOARD_SRC = path.join(__dirname, '../../packages/dashboard/src');
const SHARED_SRC = path.join(__dirname, '../../packages/shared/src');

/**
 * Get all component files
 */
function getComponentFiles(dir) {
  const files = [];

  function traverse(currentDir) {
    if (!fs.existsSync(currentDir)) return;

    const items = fs.readdirSync(currentDir);

    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory() && !item.startsWith('.')) {
        traverse(fullPath);
      } else if (
        item.match(/\.(tsx|jsx)$/) &&
        !item.includes('.test.') &&
        !item.includes('.stories.')
      ) {
        files.push(fullPath);
      }
    }
  }

  traverse(dir);
  return files;
}

/**
 * Extract component information
 */
function extractComponentInfo(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(process.cwd(), filePath);

    // Extract component name
    const componentName = path.basename(filePath, path.extname(filePath));

    // Determine atomic design level
    const atomicLevel = getAtomicLevel(filePath);

    // Extract imports (other components used)
    const componentImports = extractComponentImports(content);

    // Extract exports
    const exports = extractExports(content);

    // Analyze complexity
    const complexity = analyzeComponentComplexity(content);

    return {
      name: componentName,
      file: relativePath,
      atomicLevel,
      imports: componentImports,
      exports,
      complexity,
      isShared: filePath.includes('packages/shared'),
      feature: extractFeature(filePath),
    };
  } catch (error) {
    console.warn(`Error analyzing ${filePath}: ${error.message}`);
    return null;
  }
}

/**
 * Determine atomic design level
 */
function getAtomicLevel(filePath) {
  if (filePath.includes('/atoms/')) return 'atom';
  if (filePath.includes('/molecules/')) return 'molecule';
  if (filePath.includes('/organisms/')) return 'organism';
  if (filePath.includes('/templates/')) return 'template';
  if (filePath.includes('/pages/')) return 'page';
  if (filePath.includes('/layouts/')) return 'layout';
  return 'unknown';
}

/**
 * Extract feature from file path
 */
function extractFeature(filePath) {
  const match = filePath.match(/features\/([^\/]+)/);
  return match ? match[1] : 'shared';
}

/**
 * Extract component imports
 */
function extractComponentImports(content) {
  const imports = [];

  // Match import statements for components (capitalized)
  const importRegex =
    /import\s+(?:\{([^}]+)\}|\*\s+as\s+(\w+)|(\w+))\s+from\s+['"`]([^'"`]+)['"`]/g;

  let match;
  while ((match = importRegex.exec(content)) !== null) {
    const [, namedImports, namespaceImport, defaultImport, source] = match;

    if (namedImports) {
      // Extract named imports
      const names = namedImports.split(',').map((name) => name.trim());
      names.forEach((name) => {
        if (/^[A-Z]/.test(name)) {
          // Component names start with capital
          imports.push({
            name: name,
            source: source,
            type: 'named',
            isInternal: source.startsWith('@adhd-trading-dashboard') || source.startsWith('.'),
          });
        }
      });
    }

    if (defaultImport && /^[A-Z]/.test(defaultImport)) {
      imports.push({
        name: defaultImport,
        source: source,
        type: 'default',
        isInternal: source.startsWith('@adhd-trading-dashboard') || source.startsWith('.'),
      });
    }

    if (namespaceImport && /^[A-Z]/.test(namespaceImport)) {
      imports.push({
        name: namespaceImport,
        source: source,
        type: 'namespace',
        isInternal: source.startsWith('@adhd-trading-dashboard') || source.startsWith('.'),
      });
    }
  }

  return imports;
}

/**
 * Extract exports
 */
function extractExports(content) {
  const exports = [];

  // Default export
  const defaultExportMatch = content.match(/export\s+default\s+(\w+)/);
  if (defaultExportMatch) {
    exports.push({
      name: defaultExportMatch[1],
      type: 'default',
    });
  }

  // Named exports
  const namedExportRegex = /export\s+(?:const|function|class)\s+(\w+)/g;
  let match;
  while ((match = namedExportRegex.exec(content)) !== null) {
    exports.push({
      name: match[1],
      type: 'named',
    });
  }

  return exports;
}

/**
 * Analyze component complexity
 */
function analyzeComponentComplexity(content) {
  const lines = content.split('\n').length;
  const jsxElements = (content.match(/<\w+/g) || []).length;
  const hooks = (content.match(/use\w+\(/g) || []).length;
  const props = (content.match(/\w+\s*:\s*\w+/g) || []).length;

  return {
    lines,
    jsxElements,
    hooks,
    props,
    score: Math.round(lines / 10 + jsxElements + hooks * 2 + props),
  };
}

/**
 * Build component relationship graph
 */
function buildRelationshipGraph(components) {
  const graph = {
    nodes: new Map(),
    edges: [],
    hierarchy: new Map(),
  };

  // Create nodes
  components.forEach((component) => {
    graph.nodes.set(component.name, component);
  });

  // Create edges based on imports
  components.forEach((component) => {
    component.imports.forEach((imp) => {
      if (imp.isInternal) {
        // Find the imported component
        const importedComponent = components.find(
          (c) => c.name === imp.name || c.exports.some((e) => e.name === imp.name)
        );

        if (importedComponent) {
          graph.edges.push({
            from: component.name,
            to: importedComponent.name,
            type: 'uses',
            source: imp.source,
          });
        }
      }
    });
  });

  // Build atomic design hierarchy
  const atomicLevels = ['atom', 'molecule', 'organism', 'template', 'page'];
  atomicLevels.forEach((level) => {
    const componentsAtLevel = components.filter((c) => c.atomicLevel === level);
    graph.hierarchy.set(level, componentsAtLevel);
  });

  return graph;
}

/**
 * Analyze component usage patterns
 */
function analyzeUsagePatterns(graph) {
  const patterns = {
    mostUsed: [],
    leastUsed: [],
    crossFeature: [],
    hierarchyViolations: [],
  };

  // Calculate usage frequency
  const usageCount = new Map();
  graph.edges.forEach((edge) => {
    usageCount.set(edge.to, (usageCount.get(edge.to) || 0) + 1);
  });

  // Most and least used components
  const sortedByUsage = Array.from(usageCount.entries()).sort((a, b) => b[1] - a[1]);

  patterns.mostUsed = sortedByUsage.slice(0, 10).map(([name, count]) => ({
    name,
    count,
    component: graph.nodes.get(name),
  }));

  patterns.leastUsed = Array.from(graph.nodes.values())
    .filter((c) => !usageCount.has(c.name))
    .slice(0, 10);

  // Cross-feature usage
  graph.edges.forEach((edge) => {
    const fromComponent = graph.nodes.get(edge.from);
    const toComponent = graph.nodes.get(edge.to);

    if (
      fromComponent &&
      toComponent &&
      fromComponent.feature !== toComponent.feature &&
      fromComponent.feature !== 'shared' &&
      toComponent.feature !== 'shared'
    ) {
      patterns.crossFeature.push({
        from: fromComponent,
        to: toComponent,
      });
    }
  });

  // Hierarchy violations (e.g., atoms importing molecules)
  const levelOrder = { atom: 0, molecule: 1, organism: 2, template: 3, page: 4 };

  graph.edges.forEach((edge) => {
    const fromComponent = graph.nodes.get(edge.from);
    const toComponent = graph.nodes.get(edge.to);

    if (fromComponent && toComponent) {
      const fromLevel = levelOrder[fromComponent.atomicLevel] || 999;
      const toLevel = levelOrder[toComponent.atomicLevel] || 999;

      if (fromLevel < toLevel) {
        patterns.hierarchyViolations.push({
          from: fromComponent,
          to: toComponent,
          violation: `${fromComponent.atomicLevel} importing ${toComponent.atomicLevel}`,
        });
      }
    }
  });

  return patterns;
}

/**
 * Display results
 */
function displayResults(graph, patterns) {
  console.log(chalk.blue('🗺️  COMPONENT RELATIONSHIP ANALYSIS'));
  console.log(chalk.blue('====================================\n'));

  // Summary
  console.log(chalk.yellow('📊 SUMMARY:'));
  console.log(`   Total Components: ${graph.nodes.size}`);
  console.log(`   Component Relationships: ${graph.edges.length}`);
  console.log(`   Cross-Feature Usage: ${patterns.crossFeature.length}`);
  console.log(`   Hierarchy Violations: ${patterns.hierarchyViolations.length}\n`);

  // Atomic design breakdown
  console.log(chalk.yellow('🏗️  ATOMIC DESIGN BREAKDOWN:'));
  graph.hierarchy.forEach((components, level) => {
    console.log(`   ${level}: ${components.length} components`);
  });
  console.log('');

  // Most used components
  if (patterns.mostUsed.length > 0) {
    console.log(chalk.green('🌟 MOST USED COMPONENTS:'));
    console.log('------------------------');
    patterns.mostUsed.forEach((item, index) => {
      console.log(`${index + 1}. ${item.name} (used ${item.count} times)`);
      console.log(`   Level: ${item.component?.atomicLevel || 'unknown'}`);
      console.log(`   Feature: ${item.component?.feature || 'unknown'}\n`);
    });
  }

  // Unused components
  if (patterns.leastUsed.length > 0) {
    console.log(chalk.red('🚫 UNUSED COMPONENTS:'));
    console.log('---------------------');
    patterns.leastUsed.forEach((component, index) => {
      console.log(`${index + 1}. ${component.name}`);
      console.log(`   File: ${component.file}`);
      console.log(`   Level: ${component.atomicLevel}\n`);
    });
  }

  // Cross-feature usage
  if (patterns.crossFeature.length > 0) {
    console.log(chalk.yellow('🔄 CROSS-FEATURE USAGE:'));
    console.log('------------------------');
    patterns.crossFeature.slice(0, 10).forEach((usage, index) => {
      console.log(`${index + 1}. ${usage.from.feature} → ${usage.to.feature}`);
      console.log(`   ${usage.from.name} uses ${usage.to.name}\n`);
    });
  }

  // Hierarchy violations
  if (patterns.hierarchyViolations.length > 0) {
    console.log(chalk.red('⚠️  ATOMIC DESIGN VIOLATIONS:'));
    console.log('------------------------------');
    patterns.hierarchyViolations.forEach((violation, index) => {
      console.log(`${index + 1}. ${violation.violation}`);
      console.log(`   ${violation.from.name} → ${violation.to.name}`);
      console.log(`   File: ${violation.from.file}\n`);
    });
  }

  // Recommendations
  console.log(chalk.green('💡 RECOMMENDATIONS:'));
  console.log('--------------------');

  if (patterns.leastUsed.length > 5) {
    console.log('🧹 Consider removing unused components to reduce bundle size');
  }

  if (patterns.hierarchyViolations.length > 0) {
    console.log('🏗️  Fix atomic design hierarchy violations');
  }

  if (patterns.crossFeature.length > 10) {
    console.log('📦 Consider moving frequently cross-used components to shared package');
  }

  const highComplexityComponents = Array.from(graph.nodes.values()).filter(
    (c) => c.complexity.score > 50
  );

  if (highComplexityComponents.length > 0) {
    console.log(
      `🔧 ${highComplexityComponents.length} components have high complexity - consider refactoring`
    );
  }
}

/**
 * Main analysis function
 */
async function analyzeComponentRelationships() {
  console.log(chalk.blue('🔍 Analyzing Component Relationships...\n'));

  // Get all component files
  const dashboardComponents = getComponentFiles(DASHBOARD_SRC);
  const sharedComponents = getComponentFiles(SHARED_SRC);
  const allComponentFiles = [...dashboardComponents, ...sharedComponents];

  console.log(`📁 Found ${allComponentFiles.length} component files\n`);

  // Extract component information
  const components = allComponentFiles.map(extractComponentInfo).filter(Boolean);

  // Build relationship graph
  const graph = buildRelationshipGraph(components);

  // Analyze usage patterns
  const patterns = analyzeUsagePatterns(graph);

  // Display results
  displayResults(graph, patterns);

  return {
    graph,
    patterns,
    components,
  };
}

// Export for use in other tools
export { analyzeComponentRelationships };
export default analyzeComponentRelationships;

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  analyzeComponentRelationships().catch(console.error);
}
