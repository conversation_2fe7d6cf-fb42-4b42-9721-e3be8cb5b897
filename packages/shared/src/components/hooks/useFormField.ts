/**
 * useFormField Hook
 *
 * A custom hook for managing form field state and validation
 */

import { useState, useCallback, useMemo } from 'react';

export interface UseFormFieldReturn {
  value: string;
  error: string | null;
  touched: boolean;
  isValid: boolean;
  validating: boolean;
  valid: boolean;
  setValue: (value: string) => void;
  setError: (error: string | null) => void;
  setTouched: (touched: boolean) => void;
  reset: () => void;
  validate: () => boolean;
  handleChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => void;
  handleBlur: () => void;
}

export interface UseFormFieldOptions {
  initialValue?: string;
  validator?: (value: string) => string | null;
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
}

export const useFormField = (options: UseFormFieldOptions = {}): UseFormFieldReturn => {
  const { initialValue = '', validator, required = false, minLength, maxLength, pattern } = options;

  const [value, setValue] = useState<string>(initialValue);
  const [error, setError] = useState<string | null>(null);
  const [touched, setTouched] = useState<boolean>(false);
  const [validating, setValidating] = useState<boolean>(false);

  const validate = useCallback((): boolean => {
    let validationError: string | null = null;

    // Required validation
    if (required && !value.trim()) {
      validationError = 'This field is required';
    }
    // Min length validation
    else if (minLength && value.length < minLength) {
      validationError = `Minimum length is ${minLength} characters`;
    }
    // Max length validation
    else if (maxLength && value.length > maxLength) {
      validationError = `Maximum length is ${maxLength} characters`;
    }
    // Pattern validation
    else if (pattern && !pattern.test(value)) {
      validationError = 'Invalid format';
    }
    // Custom validator
    else if (validator) {
      validationError = validator(value);
    }

    setError(validationError);
    return validationError === null;
  }, [value, required, minLength, maxLength, pattern, validator]);

  const isValid = useMemo(() => {
    return error === null && (!required || value.trim() !== '');
  }, [error, required, value]);

  const valid = useMemo(() => {
    return error === null && (!required || value.trim() !== '');
  }, [error, required, value]);

  const reset = useCallback(() => {
    setValue(initialValue);
    setError(null);
    setTouched(false);
  }, [initialValue]);

  const handleSetValue = useCallback(
    (newValue: string) => {
      setValue(newValue);
      if (touched) {
        // Re-validate when value changes if field has been touched
        setTimeout(() => validate(), 0);
      }
    },
    [touched, validate]
  );

  const handleSetTouched = useCallback(
    (newTouched: boolean) => {
      setTouched(newTouched);
      if (newTouched) {
        // Validate when field is first touched
        setTimeout(() => validate(), 0);
      }
    },
    [validate]
  );

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
      const newValue = e.target.value;
      handleSetValue(newValue);
    },
    [handleSetValue]
  );

  const handleBlur = useCallback(() => {
    handleSetTouched(true);
  }, [handleSetTouched]);

  return {
    value,
    error,
    touched,
    isValid,
    validating,
    valid,
    setValue: handleSetValue,
    setError,
    setTouched: handleSetTouched,
    reset,
    validate,
    handleChange,
    handleBlur,
  };
};

export default useFormField;
