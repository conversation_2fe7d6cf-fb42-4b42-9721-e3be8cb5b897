import { PerformanceMetrics, CompleteTradeData, TradeFilters } from '../types/trading';
/**
 * Trade Storage Service
 */
declare class TradeStorageService {
    private dbName;
    private version;
    private db;
    private stores;
    /**
     * Initialize the database with new schema
     * @returns A promise that resolves when the database is initialized
     */
    private initDB;
    /**
     * Save a complete trade with all related details
     * @param tradeData Complete trade data including all related tables
     * @returns A promise that resolves with the saved trade ID
     */
    saveTradeWithDetails(tradeData: CompleteTradeData): Promise<number>;
    /**
     * Get a complete trade by ID with all related data
     * @param id The ID of the trade to get
     * @returns A promise that resolves with the complete trade data
     */
    getTradeById(id: number): Promise<CompleteTradeData | null>;
    /**
     * Get performance metrics from all trades
     * @returns A promise that resolves with performance metrics
     */
    getPerformanceMetrics(): Promise<PerformanceMetrics>;
    /**
     * Filter trades based on criteria
     * @param filters The filter criteria
     * @returns A promise that resolves with filtered trades
     */
    filterTrades(filters: TradeFilters): Promise<CompleteTradeData[]>;
    /**
     * Get all trades (simplified version for backward compatibility)
     * @returns A promise that resolves with all trades
     */
    getAllTrades(): Promise<CompleteTradeData[]>;
    /**
     * Delete a trade and all related data
     * @param id The ID of the trade to delete
     * @returns A promise that resolves when the trade is deleted
     */
    deleteTrade(id: number): Promise<void>;
    /**
     * Update a trade with all related data
     * @param id The trade ID to update
     * @param tradeData Updated trade data
     * @returns A promise that resolves when the trade is updated
     */
    updateTradeWithDetails(id: number, tradeData: CompleteTradeData): Promise<void>;
}
export declare const tradeStorage: TradeStorageService;
export declare const tradeStorageService: TradeStorageService;
export type { CompleteTradeData, TradeFilters, TradeRecord, TradeFvgDetails, TradeSetup, TradeAnalysisRecord, PerformanceMetrics, Trade, } from '../types/trading';
export default tradeStorage;
//# sourceMappingURL=tradeStorage.d.ts.map