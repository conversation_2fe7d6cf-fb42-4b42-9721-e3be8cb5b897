{"version": 3, "file": "tradeStorageInterface.d.ts", "sourceRoot": "", "sources": ["../../src/services/tradeStorageInterface.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAGH,cAAc,kBAAkB,CAAC;AAGjC,OAAO,KAAK,EACV,WAAW,EACX,qBAAqB,EACrB,qBAAqB,EACrB,kBAAkB,EAClB,mBAAmB,EACnB,yBAAyB,EACzB,0BAA0B,EAC1B,iBAAiB,EACjB,kBAAkB,EAClB,eAAe,EACf,uBAAuB,EACxB,MAAM,UAAU,CAAC;AAElB;;;;GAIG;AACH,MAAM,WAAW,mBAAmB;IAClC,gBAAgB,IAAI,OAAO,CAAC,qBAAqB,CAAC,CAAC;IACnD,gBAAgB,IAAI,OAAO,CAAC,qBAAqB,CAAC,CAAC;IACnD,cAAc,CAAC,OAAO,CAAC,EAAE,kBAAkB,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAC3E,qBAAqB,CAAC,OAAO,CAAC,EAAE,yBAAyB,GAAG,OAAO,CAAC,0BAA0B,CAAC,CAAC;IAChG,aAAa,CAAC,OAAO,CAAC,EAAE,iBAAiB,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;IACxE,mBAAmB,CAAC,WAAW,EAAE,eAAe,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC;IACxE,kBAAkB,IAAI,OAAO,CAAC,uBAAuB,CAAC,CAAC;CACxD"}