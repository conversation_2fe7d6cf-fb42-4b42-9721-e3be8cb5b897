import { Reducer, Action } from '../state/createStoreContext';
/**
 * Persist state options
 */
export interface PersistStateOptions<S> {
    /** The key to use for local storage */
    key: string;
    /** The initial state */
    initialState: S;
    /** The version of the state schema */
    version?: number;
    /** A function to migrate state from a previous version */
    migrate?: (state: any, version: number) => S;
    /** A function to serialize the state */
    serialize?: (state: S) => string;
    /** A function to deserialize the state */
    deserialize?: (serialized: string) => S;
    /** A function to filter the state before persisting */
    filter?: (state: S) => Partial<S>;
    /** A function to merge the persisted state with the initial state */
    merge?: (persistedState: Partial<S>, initialState: S) => S;
    /** Whether to debug the persistence */
    debug?: boolean;
}
/**
 * Persist state result
 */
export interface PersistStateResult<S, A extends Action> {
    /** The persisted reducer */
    reducer: Reducer<S, A>;
    /** The persisted initial state */
    initialState: S;
    /** A function to clear the persisted state */
    clear: () => void;
}
/**
 * Create a persisted reducer
 *
 * @param reducer - The reducer function
 * @param options - The persist state options
 * @returns The persisted reducer and initial state
 */
export declare function persistState<S, A extends Action>(reducer: Reducer<S, A>, options: PersistStateOptions<S>): PersistStateResult<S, A>;
//# sourceMappingURL=persistState.d.ts.map