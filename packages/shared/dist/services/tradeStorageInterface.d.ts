import { ApiResponse, ConfigurationResponse, DashboardDataResponse, TradingDataOptions, TradingDataResponse, PerformanceMetricsOptions, PerformanceMetricsResponse, MarketNewsOptions, MarketNewsResponse, UserPreferences, UserPreferencesResponse } from '../types';
/**
 * API Types
 *
 * This module exports type definitions for the API.
 *
 * @deprecated Use '../types' instead. This module is kept for backward compatibility.
 */
export * from '../types/trading';
/**
 * Trade Storage Service Interface
 *
 * Defines the methods that the trade storage service implements
 */
export interface TradeStorageService {
    getConfiguration(): Promise<ConfigurationResponse>;
    getDashboardData(): Promise<DashboardDataResponse>;
    getTradingData(options?: TradingDataOptions): Promise<TradingDataResponse>;
    getPerformanceMetrics(options?: PerformanceMetricsOptions): Promise<PerformanceMetricsResponse>;
    getMarketNews(options?: MarketNewsOptions): Promise<MarketNewsResponse>;
    saveUserPreferences(preferences: UserPreferences): Promise<ApiResponse>;
    getUserPreferences(): Promise<UserPreferencesResponse>;
}
//# sourceMappingURL=tradeStorageInterface.d.ts.map