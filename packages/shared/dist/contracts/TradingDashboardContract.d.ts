/**
 * Trading Dashboard Feature Contract
 *
 * Defines the interface for the Trading Dashboard feature.
 */
export interface TradingDashboardApi {
    /**
     * Get dashboard metrics
     */
    getDashboardMetrics: () => Promise<DashboardMetrics>;
    /**
     * Get performance chart data
     */
    getPerformanceData: (period: 'day' | 'week' | 'month' | 'year') => Promise<PerformanceData[]>;
    /**
     * Get setup analysis data
     */
    getSetupAnalysis: () => Promise<SetupAnalysisData[]>;
    /**
     * Refresh dashboard data
     */
    refreshData: () => Promise<void>;
}
export interface DashboardMetrics {
    totalTrades: number;
    winRate: number;
    totalPnL: number;
    avgRMultiple: number;
    bestTrade: number;
    worstTrade: number;
    currentStreak: number;
    maxDrawdown: number;
}
export interface PerformanceData {
    date: string;
    pnl: number;
    cumulativePnL: number;
    trades: number;
}
export interface SetupAnalysisData {
    setup: string;
    trades: number;
    winRate: number;
    avgPnL: number;
    totalPnL: number;
}
export interface TradingDashboardEvents {
    /**
     * Emitted when dashboard data is refreshed
     */
    onDataRefreshed: (metrics: DashboardMetrics) => void;
    /**
     * Emitted when a metric is clicked for drill-down
     */
    onMetricClicked: (metric: keyof DashboardMetrics) => void;
}
//# sourceMappingURL=TradingDashboardContract.d.ts.map