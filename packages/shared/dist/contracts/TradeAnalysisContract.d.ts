import { Trade } from '../types';
export interface TradeAnalysisApi {
    /**
     * Analyze trades with filters
     */
    analyzeTrades: (filters: TradeAnalysisFilters) => Promise<TradeAnalysisResult>;
    /**
     * Get equity curve data
     */
    getEquityCurve: (trades: Trade[]) => Promise<EquityCurveData[]>;
    /**
     * Get distribution analysis
     */
    getDistributionAnalysis: (trades: Trade[]) => Promise<DistributionData>;
    /**
     * Export analysis results
     */
    exportAnalysis: (result: TradeAnalysisResult, format: 'pdf' | 'csv') => Promise<string>;
}
export interface TradeAnalysisFilters {
    dateRange?: [Date, Date];
    direction?: 'Long' | 'Short';
    market?: string;
    setup?: string;
    minPnL?: number;
    maxPnL?: number;
    winLoss?: 'Win' | 'Loss' | 'Breakeven';
}
export interface TradeAnalysisResult {
    summary: {
        totalTrades: number;
        winRate: number;
        avgPnL: number;
        totalPnL: number;
        avgRMultiple: number;
        maxDrawdown: number;
        profitFactor: number;
    };
    bySetup: SetupPerformance[];
    byTimeOfDay: TimePerformance[];
    byDayOfWeek: DayPerformance[];
    equityCurve: EquityCurveData[];
    distribution: DistributionData;
}
export interface SetupPerformance {
    setup: string;
    trades: number;
    winRate: number;
    avgPnL: number;
    totalPnL: number;
    avgRMultiple: number;
}
export interface TimePerformance {
    hour: number;
    trades: number;
    winRate: number;
    avgPnL: number;
}
export interface DayPerformance {
    day: string;
    trades: number;
    winRate: number;
    avgPnL: number;
}
export interface EquityCurveData {
    date: string;
    cumulativePnL: number;
    drawdown: number;
}
export interface DistributionData {
    bins: number[];
    frequencies: number[];
    mean: number;
    median: number;
    stdDev: number;
}
export interface TradeAnalysisEvents {
    /**
     * Emitted when analysis is completed
     */
    onAnalysisCompleted: (result: TradeAnalysisResult) => void;
    /**
     * Emitted when filters are changed
     */
    onFiltersChanged: (filters: TradeAnalysisFilters) => void;
}
//# sourceMappingURL=TradeAnalysisContract.d.ts.map