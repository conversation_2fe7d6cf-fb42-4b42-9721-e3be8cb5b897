/**
 * Typography Tokens
 *
 * This file defines the typography tokens used throughout the application.
 */
/**
 * Font Sizes
 *
 * These are the font size values used as the foundation for the theme.
 */
export declare const fontSizes: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    xxl: string;
    h1: string;
    h2: string;
    h3: string;
    h4: string;
    h5: string;
    h6: string;
};
/**
 * Font Weights
 *
 * These are the font weight values used as the foundation for the theme.
 */
export declare const fontWeights: {
    light: number;
    regular: number;
    medium: number;
    semibold: number;
    bold: number;
};
/**
 * Line Heights
 *
 * These are the line height values used as the foundation for the theme.
 */
export declare const lineHeights: {
    tight: number;
    normal: number;
    loose: number;
};
/**
 * Font Families
 *
 * These are the font family values used as the foundation for the theme.
 */
export declare const fontFamilies: {
    sans: string;
    mono: string;
};
//# sourceMappingURL=typography.d.ts.map