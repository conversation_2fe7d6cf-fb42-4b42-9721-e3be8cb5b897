import { default as React, ReactNode } from 'react';
/**
 * Action with type and payload
 */
export interface Action<T = string, P = any> {
    type: T;
    payload?: P;
}
/**
 * Reducer function type
 */
export type Reducer<S, A extends Action> = (state: S, action: A) => S;
/**
 * Selector function type
 */
export type Selector<S, R> = (state: S) => R;
/**
 * Action creator function type
 */
export type ActionCreator<A extends Action> = (...args: any[]) => A;
/**
 * Dispatch function type
 */
export type Dispatch<A extends Action> = (action: A) => void;
/**
 * Store context type
 */
export interface StoreContext<S, A extends Action> {
    state: S;
    dispatch: Dispatch<A>;
}
/**
 * Store provider props
 */
export interface StoreProviderProps {
    children: ReactNode;
    initialState?: any;
}
/**
 * Create a store context with a reducer
 *
 * @param reducer - The reducer function
 * @param initialState - The initial state
 * @param displayName - The display name for the context
 * @returns An object with the context, provider, and hooks
 */
export declare function createStoreContext<S, A extends Action>(reducer: Reducer<S, A>, initialState: S, displayName?: string): {
    Context: React.Context<StoreContext<S, A>>;
    Provider: React.FC<StoreProviderProps>;
    useStore: () => StoreContext<S, A>;
    useSelector: <R>(selector: Selector<S, R>) => R;
    useAction: <T extends ActionCreator<A>>(actionCreator: T) => (...args: Parameters<T>) => void;
    useActions: <T extends Record<string, ActionCreator<A>>>(actionCreators: T) => { [K in keyof T]: (...args: Parameters<T[K]>) => void; };
};
//# sourceMappingURL=createStoreContext.d.ts.map