/**
 * Setup Elements Constants
 *
 * Modular setup construction elements for the ADHD Trading Dashboard
 * These constants define the atomic elements that can be combined to create
 * infinite setup combinations without pre-defining every possibility.
 */
export declare const SETUP_ELEMENTS: {
    readonly constant: {
        readonly parentArrays: readonly ["NWOG", "Old-NWOG", "NDOG", "Old-NDOG", "Monthly-FVG", "Weekly-FVG", "Daily-FVG", "15min-Top/Bottom-FVG", "1h-Top/Bottom-FVG"];
        readonly fvgTypes: readonly ["Strong-FVG", "AM-FPFVG", "PM-FPFVG", "Asia-FPFVG", "Premarket-FPFVG", "MNOR-FVG", "Macro-FVG", "News-FVG", "Top/Bottom-FVG"];
    };
    readonly action: {
        readonly liquidityEvents: readonly ["None", "London-H/L", "Premarket-H/L", "09:30-Opening-Range-H/L", "Lunch-H/L", "Prev-Day-H/L", "Prev-Week-H/L", "Monthly-H/L", "Macro-H/L"];
    };
    readonly variable: {
        readonly rdTypes: readonly ["None", "True-RD", "IMM-RD", "Dispersed-RD", "Wide-Gap-RD"];
    };
    readonly entry: {
        readonly methods: readonly ["Simple-Entry", "Complex-Entry", "Complex-Entry/Mini"];
    };
};
/**
 * Valid trading models (excluding invalid concepts)
 */
export declare const VALID_TRADING_MODELS: readonly ["RD-Cont", "FVG-RD", "Combined"];
/**
 * Type definitions for setup elements
 */
export type SetupConstant = typeof SETUP_ELEMENTS.constant.parentArrays[number] | typeof SETUP_ELEMENTS.constant.fvgTypes[number];
export type SetupAction = typeof SETUP_ELEMENTS.action.liquidityEvents[number];
export type SetupVariable = typeof SETUP_ELEMENTS.variable.rdTypes[number];
export type SetupEntry = typeof SETUP_ELEMENTS.entry.methods[number];
export type TradingModel = typeof VALID_TRADING_MODELS[number];
//# sourceMappingURL=setupElements.d.ts.map