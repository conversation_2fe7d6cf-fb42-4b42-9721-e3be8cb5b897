import { default as React } from 'react';
import { CardProps } from '../molecules/Card';
export interface DataCardProps extends Omit<CardProps, 'isLoading' | 'hasError' | 'errorMessage'> {
    /** The title of the data card */
    title: string;
    /** The content to display inside the data card */
    children: React.ReactNode;
    /** Whether the data is loading */
    isLoading?: boolean;
    /** Whether there was an error loading the data */
    hasError?: boolean;
    /** Error message to display */
    errorMessage?: string;
    /** Whether to show a retry button when there's an error */
    showRetry?: boolean;
    /** Function called when the retry button is clicked */
    onRetry?: () => void;
    /** Whether the data is empty */
    isEmpty?: boolean;
    /** Empty state message */
    emptyMessage?: string;
    /** Empty state action text */
    emptyActionText?: string;
    /** Function called when the empty state action button is clicked */
    onEmptyAction?: () => void;
    /** Action button to display in the header */
    actionButton?: React.ReactNode;
    /** Additional CSS class names */
    className?: string;
}
/**
 * DataCard Component
 *
 * A specialized card component for displaying data sections with loading and error states.
 */
export declare const DataCard: React.FC<DataCardProps>;
//# sourceMappingURL=DataCard.d.ts.map