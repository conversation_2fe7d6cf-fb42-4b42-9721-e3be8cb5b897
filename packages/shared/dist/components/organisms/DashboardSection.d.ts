import { default as React } from 'react';
export interface DashboardSectionProps {
    /** Unique identifier for the section */
    name: string;
    /** Display title for the section */
    title?: string;
    /** Content to render in the section */
    children?: React.ReactNode;
    /** Action buttons or controls */
    actions?: React.ReactNode;
    /** Loading state */
    isLoading?: boolean;
    /** Error state */
    error?: string | null;
    /** Custom className for styling */
    className?: string;
    /** Whether the section can be collapsed */
    collapsible?: boolean;
    /** Whether the section is initially collapsed */
    defaultCollapsed?: boolean;
}
declare const DashboardSection: React.FC<DashboardSectionProps>;
export default DashboardSection;
//# sourceMappingURL=DashboardSection.d.ts.map