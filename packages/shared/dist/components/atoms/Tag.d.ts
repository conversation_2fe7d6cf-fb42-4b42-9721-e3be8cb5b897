import { default as React } from 'react';
export type TagVariant = 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
export type TagSize = 'small' | 'medium' | 'large';
export interface TagProps {
    /** The content to display inside the tag */
    children: React.ReactNode;
    /** The variant of the tag */
    variant?: TagVariant;
    /** The size of the tag */
    size?: TagSize;
    /** Whether the tag is removable */
    removable?: boolean;
    /** Function called when the remove button is clicked */
    onRemove?: () => void;
    /** Additional CSS class names */
    className?: string;
    /** Optional click handler */
    onClick?: () => void;
}
/**
 * Tag Component
 *
 * A customizable tag component for categorizing content.
 */
export declare const Tag: React.FC<TagProps>;
//# sourceMappingURL=Tag.d.ts.map