import { default as React } from 'react';
export type InputSize = 'small' | 'medium' | 'large';
type CustomInputHTMLAttributes = Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'size'> & {
    size?: InputSize;
};
export interface InputProps extends CustomInputHTMLAttributes {
    /** The value of the input */
    value: string;
    /** Function called when the input value changes */
    onChange: (value: string) => void;
    /** The placeholder text */
    placeholder?: string;
    /** Whether the input is disabled */
    disabled?: boolean;
    /** The error message */
    error?: string;
    /** The input type */
    type?: string;
    /** The input name */
    name?: string;
    /** The input id */
    id?: string;
    /** Additional CSS class names */
    className?: string;
    /** Whether the input is required */
    required?: boolean;
    /** Input autocomplete attribute */
    autoComplete?: string;
    /** Label for the input */
    label?: string;
    /** Helper text to display below the input */
    helperText?: string;
    /** Icon to display at the start of the input */
    startIcon?: React.ReactNode;
    /** Icon to display at the end of the input */
    endIcon?: React.ReactNode;
    /** Whether the input is in a loading state */
    loading?: boolean;
    /** Whether the input is in a success state */
    success?: boolean;
    /** Whether the input should have a clear button */
    clearable?: boolean;
    /** Function called when the input is cleared */
    onClear?: () => void;
    /** Maximum character count */
    maxLength?: number;
    /** Whether to show character count */
    showCharCount?: boolean;
    /** Size of the input */
    size?: 'small' | 'medium' | 'large';
    /** Whether the input should be full width */
    fullWidth?: boolean;
}
/**
 * Input Component
 *
 * A customizable input component that follows the design system.
 */
export declare const Input: React.FC<InputProps>;
export {};
//# sourceMappingURL=Input.d.ts.map