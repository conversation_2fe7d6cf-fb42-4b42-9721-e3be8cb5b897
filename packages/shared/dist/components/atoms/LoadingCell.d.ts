import { default as React } from 'react';
interface LoadingCellProps {
    /** Size variant for different contexts */
    size?: 'small' | 'medium' | 'large';
    /** Custom width for the loading placeholder */
    width?: string;
    /** Custom className for styling */
    className?: string;
    /** Accessibility label */
    'aria-label'?: string;
}
/**
 * LoadingCell Component
 *
 * A reusable loading state component for table cells and similar contexts.
 * Provides consistent loading animation and sizing across the application.
 */
export declare const LoadingCell: React.FC<LoadingCellProps>;
export default LoadingCell;
//# sourceMappingURL=LoadingCell.d.ts.map