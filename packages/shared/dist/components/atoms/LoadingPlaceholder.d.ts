import { default as React } from 'react';
export type LoadingPlaceholderVariant = 'default' | 'card' | 'text' | 'list';
export type LoadingPlaceholderSize = 'small' | 'medium' | 'large' | 'custom';
export interface LoadingPlaceholderProps {
    /** The variant of the loading placeholder */
    variant?: LoadingPlaceholderVariant;
    /** The size of the loading placeholder */
    size?: LoadingPlaceholderSize;
    /** Custom height (only used when size is 'custom') */
    height?: string;
    /** Custom width (only used when size is 'custom') */
    width?: string;
    /** Text to display in the loading placeholder */
    text?: string;
    /** Whether to show a spinner */
    showSpinner?: boolean;
    /** Additional CSS class names */
    className?: string;
}
/**
 * Loading Placeholder Component
 *
 * A component for displaying loading states with customizable appearance.
 */
export declare const LoadingPlaceholder: React.FC<LoadingPlaceholderProps>;
//# sourceMappingURL=LoadingPlaceholder.d.ts.map