import { Meta, StoryObj } from '@storybook/react';
import { Badge } from './Badge';
declare const meta: Meta<typeof Badge>;
export default meta;
type Story = StoryObj<typeof Badge>;
export declare const Default: Story;
export declare const Primary: Story;
export declare const Success: Story;
export declare const Warning: Story;
export declare const Error: Story;
export declare const Solid: Story;
export declare const Outlined: Story;
export declare const Rounded: Story;
export declare const Dot: Story;
export declare const Counter: Story;
export declare const CounterWithMax: Story;
export declare const WithIcons: Story;
export declare const Small: Story;
export declare const Large: Story;
export declare const Clickable: Story;
export declare const AllVariants: Story;
export declare const AllStyles: Story;
//# sourceMappingURL=Badge.stories.d.ts.map