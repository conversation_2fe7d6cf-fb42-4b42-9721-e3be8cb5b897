import { Meta, StoryObj } from '@storybook/react';
import { Select } from './Select';
declare const meta: Meta<typeof Select>;
export default meta;
type Story = StoryObj<typeof Select>;
export declare const Default: Story;
export declare const WithLabel: Story;
export declare const WithHelperText: Story;
export declare const WithError: Story;
export declare const WithSuccess: Story;
export declare const Disabled: Story;
export declare const Required: Story;
export declare const WithIcon: Story;
export declare const GroupedOptions: Story;
export declare const WithDisabledOptions: Story;
export declare const Small: Story;
export declare const Large: Story;
export declare const AllVariants: Story;
//# sourceMappingURL=Select.stories.d.ts.map