import { Meta, StoryObj } from '@storybook/react';
import { Input } from './Input';
declare const meta: Meta<typeof Input>;
export default meta;
type Story = StoryObj<typeof Input>;
export declare const Default: Story;
export declare const WithLabel: Story;
export declare const WithHelperText: Story;
export declare const WithError: Story;
export declare const WithSuccess: Story;
export declare const Disabled: Story;
export declare const Required: Story;
export declare const WithIcons: Story;
export declare const Clearable: Story;
export declare const WithCharCount: Story;
export declare const Small: Story;
export declare const Large: Story;
export declare const FullWidth: Story;
export declare const AllVariants: Story;
//# sourceMappingURL=Input.stories.d.ts.map