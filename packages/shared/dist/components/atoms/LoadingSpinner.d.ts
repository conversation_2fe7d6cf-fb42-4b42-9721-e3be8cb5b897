import { default as React } from 'react';
export type LoadingSpinnerSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';
export type LoadingSpinnerVariant = 'primary' | 'secondary' | 'white' | 'red';
export interface LoadingSpinnerProps {
    /** Size variant */
    size?: LoadingSpinnerSize;
    /** Color variant */
    variant?: LoadingSpinnerVariant;
    /** Custom className */
    className?: string;
    /** Accessibility label */
    'aria-label'?: string;
    /** Animation speed (1 = normal, 2 = fast, 0.5 = slow) */
    speed?: number;
    /** Whether to show the F1 racing stripes */
    showStripes?: boolean;
}
/**
 * LoadingSpinner Component
 *
 * A standardized loading spinner with F1 racing theme integration.
 * Provides consistent loading animations across the application.
 *
 * @example
 * ```typescript
 * // Basic usage
 * <LoadingSpinner />
 *
 * // Large red spinner with racing stripes
 * <LoadingSpinner
 *   size="lg"
 *   variant="red"
 *   showStripes={true}
 *   speed={1.5}
 * />
 *
 * // Small secondary spinner
 * <LoadingSpinner size="sm" variant="secondary" />
 * ```
 */
export declare const LoadingSpinner: React.FC<LoadingSpinnerProps>;
export default LoadingSpinner;
//# sourceMappingURL=LoadingSpinner.d.ts.map