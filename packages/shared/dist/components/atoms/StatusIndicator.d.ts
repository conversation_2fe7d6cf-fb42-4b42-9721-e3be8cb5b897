import { default as React } from 'react';
export type StatusType = 'success' | 'error' | 'warning' | 'info' | 'neutral';
export type StatusSize = 'small' | 'medium' | 'large';
export interface StatusIndicatorProps {
    /** The type of status to display */
    status: StatusType;
    /** The size of the indicator */
    size?: StatusSize;
    /** Whether to pulse the indicator */
    pulse?: boolean;
    /** Whether to show a label next to the indicator */
    showLabel?: boolean;
    /** Custom label text (defaults to capitalized status type) */
    label?: string;
    /** Additional CSS class names */
    className?: string;
}
/**
 * Status Indicator Component
 *
 * A component for displaying status (success, error, warning, info).
 */
export declare const StatusIndicator: React.FC<StatusIndicatorProps>;
//# sourceMappingURL=StatusIndicator.d.ts.map