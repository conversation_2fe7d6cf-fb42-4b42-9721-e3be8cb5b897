import { default as React } from 'react';
export interface ButtonProps {
    /** The content to display inside the button */
    children: React.ReactNode;
    /** The variant of the button */
    variant?: 'primary' | 'secondary' | 'outline' | 'text';
    /** Whether the button is disabled */
    disabled?: boolean;
    /** Whether the button is in a loading state */
    loading?: boolean;
    /** The size of the button */
    size?: 'small' | 'medium' | 'large';
    /** Function called when the button is clicked */
    onClick?: () => void;
}
export declare const Button: React.FC<ButtonProps>;
export interface CardProps {
    /** The content to display inside the card */
    children: React.ReactNode;
    /** The title of the card */
    title?: string;
    /** Whether the card has a border */
    bordered?: boolean;
}
export declare const Card: React.FC<CardProps>;
export interface InputProps {
    /** The value of the input */
    value: string;
    /** Function called when the input value changes */
    onChange: (value: string) => void;
    /** The placeholder text */
    placeholder?: string;
    /** The label text */
    label?: string;
    /** Whether the input is disabled */
    disabled?: boolean;
    /** The error message */
    error?: string;
}
export declare const Input: React.FC<InputProps>;
export interface SelectOption {
    /** The value of the option */
    value: string;
    /** The label to display for the option */
    label: string;
}
export interface SelectProps {
    /** The options to display */
    options: SelectOption[];
    /** The selected value */
    value: string;
    /** Function called when the selection changes */
    onChange: (value: string) => void;
    /** The label text */
    label?: string;
    /** Whether the select is disabled */
    disabled?: boolean;
    /** The error message */
    error?: string;
}
export declare const Select: React.FC<SelectProps>;
//# sourceMappingURL=base.d.ts.map