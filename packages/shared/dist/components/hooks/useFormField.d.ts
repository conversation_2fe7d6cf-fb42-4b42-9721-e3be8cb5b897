/**
 * useFormField Hook
 *
 * A custom hook for managing form field state and validation
 */
export interface UseFormFieldReturn {
    value: string;
    error: string | null;
    touched: boolean;
    isValid: boolean;
    validating: boolean;
    valid: boolean;
    setValue: (value: string) => void;
    setError: (error: string | null) => void;
    setTouched: (touched: boolean) => void;
    reset: () => void;
    validate: () => boolean;
    handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
    handleBlur: () => void;
}
export interface UseFormFieldOptions {
    initialValue?: string;
    validator?: (value: string) => string | null;
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
}
export declare const useFormField: (options?: UseFormFieldOptions) => UseFormFieldReturn;
export default useFormField;
//# sourceMappingURL=useFormField.d.ts.map