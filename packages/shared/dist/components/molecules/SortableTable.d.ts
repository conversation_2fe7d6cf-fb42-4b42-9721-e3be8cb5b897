import { default as React } from 'react';
import { SortableColumn } from '../../hooks/useSortableTable';
export interface SortableTableProps<T> {
    data: T[];
    columns: SortableColumn<T>[];
    className?: string;
    emptyMessage?: string;
    defaultSort?: {
        field: keyof T;
        direction: 'asc' | 'desc';
    };
    renderCell?: (value: any, row: T, column: SortableColumn<T>) => React.ReactNode;
    onRowClick?: (row: T, index: number) => void;
    size?: 'sm' | 'md' | 'lg';
    striped?: boolean;
    hoverable?: boolean;
}
/**
 * SortableTable Component
 *
 * A reusable sortable table component with consistent styling and behavior.
 *
 * @example
 * ```typescript
 * const columns: SortableColumn<Trade>[] = [
 *   { field: 'symbol', label: 'Symbol', sortable: true },
 *   { field: 'profitLoss', label: 'P&L', sortable: true },
 *   { field: 'date', label: 'Date', sortable: true },
 * ];
 *
 * <SortableTable
 *   data={trades}
 *   columns={columns}
 *   defaultSort={{ field: 'profitLoss', direction: 'desc' }}
 *   onRowClick={(trade) => console.log('Clicked trade:', trade)}
 *   renderCell={(value, row, column) => {
 *     if (column.field === 'profitLoss') {
 *       return <ProfitLossCell amount={value} />;
 *     }
 *     return value;
 *   }}
 * />
 * ```
 */
export declare const SortableTable: <T>({ data, columns, className, emptyMessage, defaultSort, renderCell, onRowClick, size, striped, hoverable, }: SortableTableProps<T>) => React.ReactElement;
export default SortableTable;
//# sourceMappingURL=SortableTable.d.ts.map