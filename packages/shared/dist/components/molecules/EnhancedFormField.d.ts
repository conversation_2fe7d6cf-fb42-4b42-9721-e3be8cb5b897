import { default as React } from 'react';
import { FormFieldConfig } from '../../hooks/useFormField';
export interface EnhancedFormFieldProps extends FormFieldConfig {
    /** Field name/id */
    name: string;
    /** Field label */
    label?: string;
    /** Placeholder text */
    placeholder?: string;
    /** Whether the field is disabled */
    disabled?: boolean;
    /** Custom className */
    className?: string;
    /** Field size variant */
    size?: 'sm' | 'md' | 'lg';
    /** Help text to display below the field */
    helpText?: string;
    /** Input type for HTML input element */
    inputType?: 'input' | 'textarea' | 'select';
    /** Options for select fields */
    options?: Array<{
        value: string;
        label: string;
    }>;
    /** Number of rows for textarea */
    rows?: number;
    /** Callback when value changes */
    onChange?: (value: any) => void;
    /** Callback when field is blurred */
    onBlur?: () => void;
}
/**
 * EnhancedFormField Component
 *
 * An enhanced form field component that uses the useForm<PERSON>ield hook
 * for advanced validation and state management.
 *
 * @example
 * ```typescript
 * // Email field with validation
 * <EnhancedFormField
 *   name="email"
 *   label="Email Address"
 *   type="email"
 *   required={true}
 *   validationRules={[validationRules.email()]}
 *   placeholder="Enter your email"
 * />
 *
 * // Select field
 * <EnhancedFormField
 *   name="category"
 *   label="Trade Category"
 *   inputType="select"
 *   required={true}
 *   options={[
 *     { value: 'scalp', label: 'Scalp Trade' },
 *     { value: 'swing', label: 'Swing Trade' },
 *   ]}
 * />
 * ```
 */
export declare const EnhancedFormField: React.FC<EnhancedFormFieldProps>;
export default EnhancedFormField;
//# sourceMappingURL=EnhancedFormField.d.ts.map