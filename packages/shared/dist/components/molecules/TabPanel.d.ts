import { default as React } from 'react';
interface TabPanelProps {
    tabs: {
        id: string;
        label: string;
        content: React.ReactNode;
    }[];
    defaultTab?: string;
    className?: string;
    activeTab?: string;
    onTabClick?: (tabId: string) => void;
}
/**
 * TabPanel Component
 *
 * A reusable tab panel component for progressive disclosure UI.
 * Can be used in controlled or uncontrolled mode.
 */
declare const TabPanel: React.FC<TabPanelProps>;
export default TabPanel;
//# sourceMappingURL=TabPanel.d.ts.map