import { <PERSON>a, StoryObj } from '@storybook/react';
import { Table } from './Table';
declare const meta: Meta<typeof Table>;
export default meta;
type Story = StoryObj<typeof Table>;
export declare const Default: Story;
export declare const Loading: Story;
export declare const Compact: Story;
export declare const WithoutBorders: Story;
export declare const WithoutStripes: Story;
export declare const WithStickyHeader: Story;
export declare const WithoutPagination: Story;
export declare const EmptyTable: Story;
//# sourceMappingURL=Table.stories.d.ts.map