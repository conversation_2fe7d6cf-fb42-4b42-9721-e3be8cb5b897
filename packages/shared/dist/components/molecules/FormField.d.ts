import { default as React } from 'react';
export interface FormFieldProps {
    /** The form control to render (input, select, etc.) */
    children: React.ReactNode;
    /** The label text */
    label: string;
    /** Optional helper text */
    helperText?: string;
    /** Whether the field is required */
    required?: boolean;
    /** The error message */
    error?: string;
    /** Additional CSS class names */
    className?: string;
    /** The id of the form control */
    id?: string;
}
/**
 * FormField Component
 *
 * A component that combines a label with an input, select, or other form control.
 */
export declare const FormField: React.FC<FormFieldProps>;
//# sourceMappingURL=FormField.d.ts.map