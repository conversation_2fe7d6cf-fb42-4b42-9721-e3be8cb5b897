import { default as React } from 'react';
export type CardVariant = 'default' | 'primary' | 'secondary' | 'outlined' | 'elevated';
export type CardPadding = 'none' | 'small' | 'medium' | 'large';
export interface CardProps {
    /** The content to display inside the card */
    children: React.ReactNode;
    /** The title of the card */
    title?: string;
    /** The subtitle of the card */
    subtitle?: string;
    /** Whether the card has a border */
    bordered?: boolean;
    /** The variant of the card */
    variant?: CardVariant;
    /** The padding size of the card */
    padding?: CardPadding;
    /** Additional CSS class names */
    className?: string;
    /** Optional footer content */
    footer?: React.ReactNode;
    /** Optional action buttons for the header */
    actions?: React.ReactNode;
    /** Whether the card is in a loading state */
    isLoading?: boolean;
    /** Whether the card has an error */
    hasError?: boolean;
    /** Error message to display */
    errorMessage?: string;
    /** Whether the card is clickable */
    clickable?: boolean;
    /** Function called when the card is clicked */
    onClick?: () => void;
}
/**
 * Card Component
 *
 * A customizable card component that follows the design system.
 */
export declare const Card: React.FC<CardProps>;
//# sourceMappingURL=Card.d.ts.map