import { default as React } from 'react';
export interface TableColumn<T extends Record<string, any>> {
    /** Unique identifier for the column */
    id: string;
    /** Header text for the column */
    header: React.ReactNode;
    /** Function to render the cell content */
    cell: (row: T, rowIndex: number) => React.ReactNode;
    /** Whether the column is sortable */
    sortable?: boolean;
    /** Width of the column (e.g., '100px', '20%') */
    width?: string;
    /** Whether the column is hidden */
    hidden?: boolean;
    /** Alignment of the column content */
    align?: 'left' | 'center' | 'right';
}
export interface TableProps<T extends Record<string, any>> {
    /** The columns configuration */
    columns: TableColumn<T>[];
    /** The data to display */
    data: T[];
    /** Whether the table is loading */
    isLoading?: boolean;
    /** Whether the table has a border */
    bordered?: boolean;
    /** Whether the table has striped rows */
    striped?: boolean;
    /** Whether the table has hoverable rows */
    hoverable?: boolean;
    /** Whether the table is compact */
    compact?: boolean;
    /** Whether the table has a sticky header */
    stickyHeader?: boolean;
    /** The height of the table (e.g., '400px') */
    height?: string;
    /** Function called when a row is clicked */
    onRowClick?: (row: T, index: number) => void;
    /** Function to determine if a row is selected */
    isRowSelected?: (row: T, index: number) => boolean;
    /** Function called when a sort header is clicked */
    onSort?: (columnId: string, direction: 'asc' | 'desc') => void;
    /** The ID of the column to sort by */
    sortColumn?: string;
    /** The direction to sort */
    sortDirection?: 'asc' | 'desc';
    /** Whether the table has pagination */
    pagination?: boolean;
    /** The current page */
    currentPage?: number;
    /** The number of rows per page */
    pageSize?: number;
    /** The total number of rows */
    totalRows?: number;
    /** Function called when the page changes */
    onPageChange?: (page: number) => void;
    /** Function called when the page size changes */
    onPageSizeChange?: (pageSize: number) => void;
    /** Additional CSS class names */
    className?: string;
    /** Empty state message */
    emptyMessage?: string;
    /** Whether the table is scrollable horizontally */
    scrollable?: boolean;
}
/**
 * Table Component
 *
 * A customizable table component that follows the design system.
 */
export declare function Table<T extends Record<string, any>>({ columns, data, isLoading, bordered, striped, hoverable, compact, stickyHeader, height, onRowClick, isRowSelected, onSort, sortColumn, sortDirection, pagination, currentPage, pageSize, totalRows, onPageChange, onPageSizeChange, className, emptyMessage, scrollable, }: TableProps<T>): JSX.Element;
//# sourceMappingURL=Table.d.ts.map