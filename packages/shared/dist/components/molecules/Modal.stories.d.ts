import { <PERSON>a, StoryObj } from '@storybook/react';
import { Modal } from './Modal';
declare const meta: Meta<typeof Modal>;
export default meta;
type Story = StoryObj<typeof Modal>;
export declare const Default: Story;
export declare const WithForm: Story;
export declare const Small: Story;
export declare const Large: Story;
export declare const WithoutFooter: Story;
export declare const WithCustomFooter: Story;
export declare const LoadingAction: Story;
export declare const LongContent: Story;
export declare const Confirmation: Story;
export declare const WithoutTitle: Story;
//# sourceMappingURL=Modal.stories.d.ts.map