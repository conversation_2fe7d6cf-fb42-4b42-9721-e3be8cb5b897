import { default as React } from 'react';
import { CompleteTradeData } from '../../types/trading';
export declare const TRADE_COLUMN_IDS: {
    readonly DATE: "date";
    readonly SYMBOL: "symbol";
    readonly DIRECTION: "direction";
    readonly MODEL_TYPE: "model_type";
    readonly SESSION: "session";
    readonly ENTRY_PRICE: "entry_price";
    readonly EXIT_PRICE: "exit_price";
    readonly R_MULTIPLE: "r_multiple";
    readonly ACHIEVED_PL: "achieved_pl";
    readonly WIN_LOSS: "win_loss";
    readonly PATTERN_QUALITY: "pattern_quality_rating";
    readonly ENTRY_TIME: "entry_time";
    readonly EXIT_TIME: "exit_time";
};
export interface TableColumn<T extends Record<string, any>> {
    /** Unique identifier for the column */
    id: string;
    /** Header text for the column */
    header: React.ReactNode;
    /** Function to render the cell content */
    cell: (row: T, rowIndex: number) => React.ReactNode;
    /** Whether the column is sortable */
    sortable?: boolean;
    /** Width of the column (e.g., '100px', '20%') */
    width?: string;
    /** Whether the column is hidden */
    hidden?: boolean;
    /** Alignment of the column content */
    align?: 'left' | 'center' | 'right';
}
export declare const formatTime: (timeString: string | undefined) => string;
/**
 * Default column definitions for trade tables
 */
export declare const getTradeTableColumns: () => TableColumn<CompleteTradeData>[];
/**
 * Compact column definitions for smaller displays
 */
export declare const getCompactTradeTableColumns: () => TableColumn<CompleteTradeData>[];
/**
 * Performance-focused column definitions
 */
export declare const getPerformanceTradeTableColumns: () => TableColumn<CompleteTradeData>[];
//# sourceMappingURL=TradeTableColumns.d.ts.map