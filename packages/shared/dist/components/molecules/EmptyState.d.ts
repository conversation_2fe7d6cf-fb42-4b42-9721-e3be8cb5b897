import { default as React } from 'react';
export type EmptyStateVariant = 'default' | 'compact' | 'card';
export type EmptyStateSize = 'small' | 'medium' | 'large';
export interface EmptyStateProps {
    /** The title of the empty state */
    title?: string;
    /** The description of the empty state */
    description?: string;
    /** The icon to display (as a component) */
    icon?: React.ReactNode;
    /** The action button text */
    actionText?: string;
    /** Function called when the action button is clicked */
    onAction?: () => void;
    /** The variant of the empty state */
    variant?: EmptyStateVariant;
    /** The size of the empty state */
    size?: EmptyStateSize;
    /** Additional CSS class names */
    className?: string;
    /** Additional content to display */
    children?: React.ReactNode;
}
/**
 * Empty State Component
 *
 * A component for displaying empty states with customizable appearance.
 */
export declare const EmptyState: React.FC<EmptyStateProps>;
//# sourceMappingURL=EmptyState.d.ts.map