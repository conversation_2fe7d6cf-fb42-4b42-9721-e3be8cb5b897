import { Component, ErrorInfo, ReactNode } from 'react';
export interface ErrorBoundaryProps {
    /** The children to render */
    children: ReactNode;
    /** Custom fallback component to render when an error occurs */
    fallback?: ReactNode | ((props: {
        error: Error;
        resetError: () => void;
    }) => ReactNode);
    /** Function to call when an error occurs */
    onError?: (error: Error, errorInfo: ErrorInfo) => void;
    /** Whether to reset the error boundary when the children prop changes */
    resetOnPropsChange?: boolean;
    /** Whether to reset the error boundary when the component unmounts */
    resetOnUnmount?: boolean;
    /** Name of the boundary for identification in logs */
    name?: string;
    /** Whether this is a feature-level boundary */
    isFeatureBoundary?: boolean;
    /** Function to call when the user chooses to skip a feature (only for feature boundaries) */
    onSkip?: () => void;
}
interface ErrorBoundaryState {
    /** Whether an error has occurred */
    hasError: boolean;
    /** The error that occurred */
    error: Error | null;
}
/**
 * Error Boundary Component
 *
 * A unified React error boundary component that catches errors in its child component tree
 * and displays a fallback UI instead of crashing the entire application.
 *
 * This component can be used at both the application level and feature level.
 */
export declare class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
    constructor(props: ErrorBoundaryProps);
    static getDerivedStateFromError(error: Error): ErrorBoundaryState;
    componentDidCatch(error: Error, errorInfo: ErrorInfo): void;
    componentDidUpdate(prevProps: ErrorBoundaryProps): void;
    componentWillUnmount(): void;
    resetError: () => void;
    render(): ReactNode;
}
export default ErrorBoundary;
//# sourceMappingURL=ErrorBoundary.d.ts.map