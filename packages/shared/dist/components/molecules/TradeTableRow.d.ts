import { default as React } from 'react';
import { CompleteTradeData } from '../../types/trading';
import { TableColumn } from './TradeTableColumns';
export interface TradeTableRowProps {
    /** The trade data for this row */
    trade: CompleteTradeData;
    /** The row index */
    index: number;
    /** Column definitions */
    columns: TableColumn<CompleteTradeData>[];
    /** Whether the row is selected */
    isSelected?: boolean;
    /** Whether the row is hoverable */
    hoverable?: boolean;
    /** Whether the table has striped rows */
    striped?: boolean;
    /** Whether the row is expandable */
    expandable?: boolean;
    /** Whether the row is expanded */
    isExpanded?: boolean;
    /** Function called when the row is clicked */
    onRowClick?: (trade: CompleteTradeData, index: number) => void;
    /** Function called when the row expand toggle is clicked */
    onToggleExpand?: (trade: CompleteTradeData, index: number) => void;
    /** Custom expanded content */
    expandedContent?: React.ReactNode;
}
/**
 * Trade Table Row Component
 */
export declare const TradeTableRow: React.FC<TradeTableRowProps>;
//# sourceMappingURL=TradeTableRow.d.ts.map