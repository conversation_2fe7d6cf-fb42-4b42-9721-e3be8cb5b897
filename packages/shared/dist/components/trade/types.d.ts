/**
 * Trade Component Types
 *
 * Shared types for trade-specific components
 */
export interface TradeMetric {
    label: string;
    value: string | number;
    positive?: boolean;
    negative?: boolean;
}
export interface TradeAnalysisData {
    title: string;
    content: React.ReactNode;
}
export interface SetupBuilderConfig {
    showPreview?: boolean;
    allowOptionalElements?: boolean;
    theme?: 'light' | 'dark' | 'f1';
}
//# sourceMappingURL=types.d.ts.map