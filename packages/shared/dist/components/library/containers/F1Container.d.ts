import { default as React, ReactNode } from 'react';
export interface F1ContainerProps {
    /** Container content */
    children: ReactNode;
    /** Container variant */
    variant?: 'dashboard' | 'form' | 'analysis' | 'settings';
    /** Maximum width */
    maxWidth?: string | number;
    /** Padding size */
    padding?: 'sm' | 'md' | 'lg' | 'xl';
    /** Whether to show loading state */
    isLoading?: boolean;
    /** Error state */
    error?: string | null;
    /** Loading fallback component */
    loadingFallback?: ReactNode;
    /** Error fallback component */
    errorFallback?: ReactNode;
    /** Custom className */
    className?: string;
    /** Whether to enable animations */
    animated?: boolean;
    /** Background variant */
    background?: 'default' | 'surface' | 'elevated';
}
/**
 * F1Container Component
 *
 * Base container component that provides consistent layout, error handling,
 * and loading states following the F1 racing theme.
 *
 * @example
 * ```typescript
 * // Dashboard container
 * <F1Container variant="dashboard" maxWidth={1400}>
 *   <DashboardContent />
 * </F1Container>
 *
 * // Form container with loading
 * <F1Container variant="form" isLoading={isSubmitting}>
 *   <TradeForm />
 * </F1Container>
 *
 * // Analysis container with error handling
 * <F1Container variant="analysis" error={error}>
 *   <AnalysisContent />
 * </F1Container>
 * ```
 */
export declare const F1Container: React.FC<F1ContainerProps>;
export default F1Container;
//# sourceMappingURL=F1Container.d.ts.map