import { default as React, ReactNode, FormEvent } from 'react';
export interface F1FormProps {
    /** Form content */
    children: ReactNode;
    /** Form submission handler */
    onSubmit?: (e: FormEvent<HTMLFormElement>) => void | Promise<void>;
    /** Form title */
    title?: string;
    /** Form subtitle */
    subtitle?: string;
    /** Whether form is submitting */
    isSubmitting?: boolean;
    /** Form error message */
    error?: string | null;
    /** Success message */
    success?: string | null;
    /** Form variant */
    variant?: 'quick' | 'detailed' | 'modal' | 'inline';
    /** Whether to show F1 accent border */
    showAccent?: boolean;
    /** Custom className */
    className?: string;
    /** Whether form is disabled */
    disabled?: boolean;
    /** Auto-save functionality */
    autoSave?: boolean;
    /** Auto-save interval in ms */
    autoSaveInterval?: number;
}
/**
 * F1Form Component
 *
 * Standardized form component that provides consistent styling,
 * validation support, and F1 racing theme.
 *
 * @example
 * ```typescript
 * // Quick trade form
 * <F1Form
 *   title="🏎️ Quick Trade Entry"
 *   variant="quick"
 *   showAccent={true}
 *   onSubmit={handleSubmit}
 *   isSubmitting={isLoading}
 *   error={error}
 *   autoSave={true}
 * >
 *   <TradeFormFields />
 * </F1Form>
 *
 * // Detailed form
 * <F1Form
 *   title="Trade Analysis"
 *   subtitle="Comprehensive trade details"
 *   variant="detailed"
 *   onSubmit={handleSubmit}
 * >
 *   <DetailedFormFields />
 * </F1Form>
 * ```
 */
export declare const F1Form: React.FC<F1FormProps>;
export default F1Form;
//# sourceMappingURL=F1Form.d.ts.map