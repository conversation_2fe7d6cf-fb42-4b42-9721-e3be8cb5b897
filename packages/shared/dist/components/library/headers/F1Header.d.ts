import { default as React, ReactNode } from 'react';
export interface F1HeaderProps {
    /** Main title */
    title: string;
    /** Subtitle/description */
    subtitle?: string;
    /** Whether to show live indicator */
    isLive?: boolean;
    /** Live status text */
    liveText?: string;
    /** Additional status text */
    statusText?: string;
    /** Refresh function */
    onRefresh?: () => void;
    /** Whether refresh is in progress */
    isRefreshing?: boolean;
    /** Custom actions */
    actions?: ReactNode;
    /** Header variant */
    variant?: 'dashboard' | 'analysis' | 'form' | 'guide' | 'settings';
    /** Custom className */
    className?: string;
}
/**
 * F1Header Component
 *
 * Standardized header component that provides consistent branding
 * and functionality across all features.
 *
 * @example
 * ```typescript
 * // Dashboard header
 * <F1Header
 *   title="Trading 2025 Dashboard"
 *   subtitle="Live Trading Session"
 *   variant="dashboard"
 *   isLive={true}
 *   onRefresh={handleRefresh}
 *   isRefreshing={isLoading}
 * />
 *
 * // Analysis header
 * <F1Header
 *   title="Trade Analysis"
 *   subtitle="Performance Metrics & Insights"
 *   variant="analysis"
 *   statusText="LIVE DATA"
 *   onRefresh={refreshData}
 * />
 *
 * // Form header with custom actions
 * <F1Header
 *   title="Trade Entry"
 *   subtitle="Quick Trade Form"
 *   variant="form"
 *   actions={<SaveButton />}
 * />
 * ```
 */
export declare const F1Header: React.FC<F1HeaderProps>;
export default F1Header;
//# sourceMappingURL=F1Header.d.ts.map