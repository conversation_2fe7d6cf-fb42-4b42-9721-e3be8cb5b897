export interface AsyncDataState<T> {
    /** The data returned from the async function */
    data: T | null;
    /** Whether the async function is currently loading */
    isLoading: boolean;
    /** Any error that occurred during the async function */
    error: Error | null;
    /** Whether the async function has been called at least once */
    isInitialized: boolean;
}
export interface UseAsyncDataOptions {
    /** Whether to fetch data immediately on mount */
    fetchOnMount?: boolean;
    /** Dependencies array for refetching when values change */
    dependencies?: any[];
}
/**
 * useAsyncData Hook
 *
 * @param asyncFn - The async function to call
 * @param options - Options for the hook
 * @returns An object with data, loading, and error states, plus a fetch function
 */
export declare function useAsyncData<T, P extends any[] = []>(asyncFn: (...params: P) => Promise<T>, options?: UseAsyncDataOptions): {
    fetchData: (...params: P) => Promise<T>;
    refetch: () => Promise<T>;
    /** The data returned from the async function */
    data: T;
    /** Whether the async function is currently loading */
    isLoading: boolean;
    /** Any error that occurred during the async function */
    error: Error | null;
    /** Whether the async function has been called at least once */
    isInitialized: boolean;
};
//# sourceMappingURL=useAsyncData.d.ts.map