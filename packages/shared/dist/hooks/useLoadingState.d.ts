/**
 * useLoadingState Hook
 *
 * EXTRACTED FROM: Multiple components with loading patterns
 * Centralizes loading state management with error handling and async operations.
 *
 * BENEFITS:
 * - Consistent loading patterns across components
 * - Built-in error handling
 * - Automatic loading state management
 * - Type-safe async operations
 */
export interface LoadingState {
    isLoading: boolean;
    error: string | null;
    isSuccess: boolean;
    isError: boolean;
}
export interface LoadingActions {
    setLoading: (loading: boolean) => void;
    setError: (error: string | null) => void;
    clearError: () => void;
    reset: () => void;
    withLoading: <T>(asyncFn: () => Promise<T>) => Promise<T>;
    withLoadingCallback: <T extends any[]>(asyncFn: (...args: T) => Promise<any>) => (...args: T) => Promise<void>;
}
export type UseLoadingStateReturn = LoadingState & LoadingActions;
/**
 * Custom hook for managing loading states with error handling
 *
 * @param initialLoading - Initial loading state (default: false)
 * @returns Object with loading state and actions
 *
 * @example
 * ```typescript
 * const { isLoading, error, withLoading } = useLoadingState();
 *
 * const fetchData = async () => {
 *   const data = await withLoading(() => api.getData());
 *   setData(data);
 * };
 * ```
 */
export declare const useLoadingState: (initialLoading?: boolean) => UseLoadingStateReturn;
export default useLoadingState;
//# sourceMappingURL=useLoadingState.d.ts.map