{"version": 3, "file": "useLoadingState.d.ts", "sourceRoot": "", "sources": ["../../src/hooks/useLoadingState.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;GAWG;AAIH,MAAM,WAAW,YAAY;IAC3B,SAAS,EAAE,OAAO,CAAC;IACnB,KAAK,EAAE,MAAM,GAAG,IAAI,CAAC;IACrB,SAAS,EAAE,OAAO,CAAC;IACnB,OAAO,EAAE,OAAO,CAAC;CAClB;AAED,MAAM,WAAW,cAAc;IAC7B,UAAU,EAAE,CAAC,OAAO,EAAE,OAAO,KAAK,IAAI,CAAC;IACvC,QAAQ,EAAE,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI,KAAK,IAAI,CAAC;IACzC,UAAU,EAAE,MAAM,IAAI,CAAC;IACvB,KAAK,EAAE,MAAM,IAAI,CAAC;IAClB,WAAW,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC;IAC1D,mBAAmB,EAAE,CAAC,CAAC,SAAS,GAAG,EAAE,EACnC,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,KAAK,OAAO,CAAC,GAAG,CAAC,KAClC,CAAC,GAAG,IAAI,EAAE,CAAC,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;CACpC;AAED,MAAM,MAAM,qBAAqB,GAAG,YAAY,GAAG,cAAc,CAAC;AAElE;;;;;;;;;;;;;;;GAeG;AACH,eAAO,MAAM,eAAe,GAAI,iBAAgB,OAAe,KAAG,qBA+EjE,CAAC;AAEF,eAAe,eAAe,CAAC"}