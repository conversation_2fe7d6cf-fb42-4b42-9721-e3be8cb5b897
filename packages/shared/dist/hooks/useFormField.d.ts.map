{"version": 3, "file": "useFormField.d.ts", "sourceRoot": "", "sources": ["../../src/hooks/useFormField.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;GAYG;AAIH,MAAM,MAAM,cAAc,CAAC,CAAC,GAAG,GAAG,IAAI;IACpC,QAAQ,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,OAAO,CAAC;IAChC,OAAO,EAAE,MAAM,CAAC;CACjB,CAAC;AAEF,MAAM,MAAM,aAAa,GAAG,MAAM,GAAG,OAAO,GAAG,UAAU,GAAG,QAAQ,GAAG,KAAK,GAAG,KAAK,GAAG,QAAQ,CAAC;AAEhG,MAAM,WAAW,eAAe,CAAC,CAAC,GAAG,GAAG;IACtC,YAAY,CAAC,EAAE,CAAC,CAAC;IACjB,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,IAAI,CAAC,EAAE,aAAa,CAAC;IACrB,eAAe,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC;IACtC,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB,SAAS,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK,CAAC,CAAC;CAC/B;AAED,MAAM,WAAW,cAAc,CAAC,CAAC,GAAG,GAAG;IACrC,KAAK,EAAE,CAAC,CAAC;IACT,KAAK,EAAE,MAAM,GAAG,IAAI,CAAC;IACrB,OAAO,EAAE,OAAO,CAAC;IACjB,KAAK,EAAE,OAAO,CAAC;IACf,KAAK,EAAE,OAAO,CAAC;IACf,UAAU,EAAE,OAAO,CAAC;CACrB;AAED,MAAM,WAAW,gBAAgB,CAAC,CAAC,GAAG,GAAG;IACvC,QAAQ,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,IAAI,CAAC;IAC7B,QAAQ,EAAE,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI,KAAK,IAAI,CAAC;IACzC,UAAU,EAAE,CAAC,OAAO,EAAE,OAAO,KAAK,IAAI,CAAC;IACvC,QAAQ,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,CAAC;IACjC,KAAK,EAAE,MAAM,IAAI,CAAC;IAClB,YAAY,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,WAAW,CAAC,gBAAgB,GAAG,iBAAiB,GAAG,mBAAmB,CAAC,KAAK,IAAI,CAAC;IACzG,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC,gBAAgB,GAAG,iBAAiB,GAAG,mBAAmB,CAAC,KAAK,IAAI,CAAC;CACvG;AAED,MAAM,MAAM,kBAAkB,CAAC,CAAC,GAAG,GAAG,IAAI,cAAc,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAElF;;GAEG;AACH,eAAO,MAAM,eAAe;eACf,CAAC,YAAW,MAAM,KAA8B,cAAc,CAAC,CAAC,CAAC;sBAU3D,MAAM,KAA0C,cAAc,CAAC,MAAM,CAAC;qBAQtE,MAAM,YAAY,MAAM,KAAG,cAAc,CAAC,MAAM,CAAC;qBAKjD,MAAM,YAAY,MAAM,KAAG,cAAc,CAAC,MAAM,CAAC;eAKvD,MAAM,YAAY,MAAM,KAAG,cAAc,CAAC,MAAM,CAAC;eAKjD,MAAM,YAAY,MAAM,KAAG,cAAc,CAAC,MAAM,CAAC;qBAK3C,MAAM,WAAW,MAAM,KAAG,cAAc,CAAC,MAAM,CAAC;CAIlE,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH,eAAO,MAAM,YAAY,GAAI,CAAC,GAAG,MAAM,EAAE,SAAQ,eAAe,CAAC,CAAC,CAAM,KAAG,kBAAkB,CAAC,CAAC,CA6H9F,CAAC;AAEF,eAAe,YAAY,CAAC"}