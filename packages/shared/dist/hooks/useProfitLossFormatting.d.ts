/**
 * useProfitLossFormatting Hook
 *
 * EXTRACTED FROM: ProfitLossCell.tsx (reducing complexity)
 * Custom hook for handling profit/loss formatting logic and state determination.
 */
export interface ProfitLossState {
    formattedAmount: string;
    isProfit: boolean;
    isLoss: boolean;
    isNeutral: boolean;
    isEmpty: boolean;
    ariaLabel: string;
}
export interface ProfitLossFormattingOptions {
    currency?: string;
    showPositiveSign?: boolean;
    customAriaLabel?: string;
}
/**
 * Custom hook for profit/loss formatting and state determination
 */
export declare const useProfitLossFormatting: (amount: number | null | undefined, options?: ProfitLossFormattingOptions) => ProfitLossState;
export default useProfitLossFormatting;
//# sourceMappingURL=useProfitLossFormatting.d.ts.map