/**
 * useLocalStorage Hook
 *
 * Custom hook for managing localStorage.
 */
/**
 * Custom hook for managing localStorage
 * @param key - The localStorage key
 * @param initialValue - The initial value
 * @returns [storedValue, setValue] - The stored value and a function to update it
 */
export declare function useLocalStorage<T>(key: string, initialValue: T): [T, (value: T | ((val: T) => T)) => void];
//# sourceMappingURL=useLocalStorage.d.ts.map