{"version": 3, "file": "useErrorHandler.d.ts", "sourceRoot": "", "sources": ["../../src/hooks/useErrorHandler.ts"], "names": [], "mappings": "AAQA,MAAM,WAAW,mBAAmB;IAClC,gDAAgD;IAChD,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,2CAA2C;IAC3C,YAAY,CAAC,EAAE,OAAO,CAAC;IACvB,qDAAqD;IACrD,kBAAkB,CAAC,EAAE,OAAO,CAAC;IAC7B,2BAA2B;IAC3B,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC;CAClC;AAED;;;;;GAKG;AACH,wBAAgB,eAAe,CAAC,OAAO,GAAE,mBAAwB;;;yBAWrD,KAAK;;iBA+CN,CAAC,MAAM,MAAM,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,KAAG,OAAO,CAAC,CAAC,GAAG,SAAS,CAAC;EA0B9D;AAED,eAAe,eAAe,CAAC"}