/**
 * useSortableTable Hook
 *
 * EXTRACTED FROM: CategoryPerformanceChart and other table components
 * Centralizes sortable table logic for consistent behavior across components.
 *
 * BENEFITS:
 * - Consistent sorting behavior
 * - Type-safe sorting configuration
 * - Reduced boilerplate in table components
 * - Automatic sort state management
 */
export type SortDirection = 'asc' | 'desc';
export interface SortConfig<T> {
    field: keyof T;
    direction: SortDirection;
}
export interface SortableColumn<T> {
    field: keyof T;
    label: string;
    sortable?: boolean;
    sortFn?: (a: T, b: T) => number;
}
export interface UseSortableTableConfig<T> {
    data: T[];
    columns: SortableColumn<T>[];
    defaultSort?: {
        field: keyof T;
        direction: SortDirection;
    };
}
export interface UseSortableTableReturn<T> {
    sortedData: T[];
    sortConfig: SortConfig<T> | null;
    handleSort: (field: keyof T) => void;
    getSortIcon: (field: keyof T) => string | null;
    isSorted: (field: keyof T) => boolean;
    getSortDirection: (field: keyof T) => SortDirection | null;
}
/**
 * Default sort functions for common data types
 */
export declare const sortFunctions: {
    string: <T>(field: keyof T) => (a: T, b: T) => number;
    number: <T>(field: keyof T) => (a: T, b: T) => number;
    date: <T>(field: keyof T) => (a: T, b: T) => number;
    boolean: <T>(field: keyof T) => (a: T, b: T) => number;
};
/**
 * Custom hook for managing sortable table state and logic
 *
 * @param config - Configuration object for the sortable table
 * @returns Object with sorted data and sort management functions
 *
 * @example
 * ```typescript
 * const tableConfig = useSortableTable({
 *   data: trades,
 *   columns: [
 *     { field: 'symbol', label: 'Symbol', sortable: true },
 *     { field: 'profitLoss', label: 'P&L', sortable: true, sortFn: sortFunctions.number('profitLoss') },
 *     { field: 'date', label: 'Date', sortable: true, sortFn: sortFunctions.date('date') },
 *   ],
 *   defaultSort: { field: 'profitLoss', direction: 'desc' },
 * });
 *
 * // In component render:
 * <th onClick={() => tableConfig.handleSort('symbol')}>
 *   Symbol {tableConfig.getSortIcon('symbol')}
 * </th>
 * ```
 */
export declare const useSortableTable: <T>({ data, columns, defaultSort, }: UseSortableTableConfig<T>) => UseSortableTableReturn<T>;
export default useSortableTable;
//# sourceMappingURL=useSortableTable.d.ts.map