/**
 * useDataFormatting Hook
 *
 * EXTRACTED FROM: Multiple components with formatting logic
 * Centralizes data formatting functions for consistent display across components.
 *
 * BENEFITS:
 * - Consistent formatting across the app
 * - Locale-aware formatting
 * - Customizable formatting options
 * - Reduced duplication of formatting logic
 */
export interface CurrencyFormatOptions {
    currency?: string;
    locale?: string;
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
    showPositiveSign?: boolean;
}
export interface PercentFormatOptions {
    locale?: string;
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
    showPositiveSign?: boolean;
}
export interface NumberFormatOptions {
    locale?: string;
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
    useGrouping?: boolean;
}
export interface UseDataFormattingReturn {
    formatCurrency: (value: number, options?: CurrencyFormatOptions) => string;
    formatPercent: (value: number, options?: PercentFormatOptions) => string;
    formatNumber: (value: number, options?: NumberFormatOptions) => string;
    formatDate: (date: Date | string, format?: 'short' | 'medium' | 'long' | 'full') => string;
    formatTime: (date: Date | string, format?: 'short' | 'medium' | 'long') => string;
    formatRelativeTime: (date: Date | string) => string;
}
/**
 * Custom hook for data formatting with consistent options
 *
 * @param defaultLocale - Default locale for formatting (default: 'en-US')
 * @returns Object with formatting functions
 *
 * @example
 * ```typescript
 * const { formatCurrency, formatPercent, formatDate } = useDataFormatting();
 *
 * const formattedPrice = formatCurrency(1234.56); // "$1,234.56"
 * const formattedPercent = formatPercent(75.5); // "75.50%"
 * const formattedDate = formatDate(new Date(), 'short'); // "12/25/2023"
 * ```
 */
export declare const useDataFormatting: (defaultLocale?: string) => UseDataFormattingReturn;
export default useDataFormatting;
//# sourceMappingURL=useDataFormatting.d.ts.map