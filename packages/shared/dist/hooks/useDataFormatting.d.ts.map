{"version": 3, "file": "useDataFormatting.d.ts", "sourceRoot": "", "sources": ["../../src/hooks/useDataFormatting.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;GAWG;AAIH,MAAM,WAAW,qBAAqB;IACpC,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAC/B,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAC/B,gBAAgB,CAAC,EAAE,OAAO,CAAC;CAC5B;AAED,MAAM,WAAW,oBAAoB;IACnC,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAC/B,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAC/B,gBAAgB,CAAC,EAAE,OAAO,CAAC;CAC5B;AAED,MAAM,WAAW,mBAAmB;IAClC,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAC/B,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAC/B,WAAW,CAAC,EAAE,OAAO,CAAC;CACvB;AAED,MAAM,WAAW,uBAAuB;IACtC,cAAc,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,qBAAqB,KAAK,MAAM,CAAC;IAC3E,aAAa,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,oBAAoB,KAAK,MAAM,CAAC;IACzE,YAAY,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,mBAAmB,KAAK,MAAM,CAAC;IACvE,UAAU,EAAE,CAAC,IAAI,EAAE,IAAI,GAAG,MAAM,EAAE,MAAM,CAAC,EAAE,OAAO,GAAG,QAAQ,GAAG,MAAM,GAAG,MAAM,KAAK,MAAM,CAAC;IAC3F,UAAU,EAAE,CAAC,IAAI,EAAE,IAAI,GAAG,MAAM,EAAE,MAAM,CAAC,EAAE,OAAO,GAAG,QAAQ,GAAG,MAAM,KAAK,MAAM,CAAC;IAClF,kBAAkB,EAAE,CAAC,IAAI,EAAE,IAAI,GAAG,MAAM,KAAK,MAAM,CAAC;CACrD;AAED;;;;;;;;;;;;;;GAcG;AACH,eAAO,MAAM,iBAAiB,GAAI,gBAAe,MAAgB,KAAG,uBAwKnE,CAAC;AAEF,eAAe,iBAAiB,CAAC"}