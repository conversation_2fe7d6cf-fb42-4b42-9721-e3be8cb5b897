/**
 * useDataSection Hook
 *
 * EXTRACTED FROM: Multiple dashboard and data display components
 * Standardizes data section patterns with loading, error, and empty states.
 *
 * BENEFITS:
 * - Consistent data section behavior
 * - Built-in loading/error/empty state handling
 * - Automatic refresh capabilities
 * - Type-safe data operations
 * - Reduced boilerplate in data components
 */
export interface DataSectionConfig<T> {
    /** Function to fetch the data */
    fetchData: () => Promise<T>;
    /** Initial data value */
    initialData?: T;
    /** Whether to fetch data on mount */
    fetchOnMount?: boolean;
    /** Refresh interval in milliseconds */
    refreshInterval?: number;
    /** Function to determine if data is empty */
    isEmpty?: (data: T) => boolean;
    /** Custom error message transformer */
    transformError?: (error: Error) => string;
    /** Dependencies that trigger a refetch when changed */
    dependencies?: any[];
}
export interface DataSectionState<T> {
    data: T | null;
    isLoading: boolean;
    error: string | null;
    isEmpty: boolean;
    isSuccess: boolean;
    isError: boolean;
    lastFetched: Date | null;
}
export interface DataSectionActions {
    refresh: () => Promise<void>;
    clearError: () => void;
    reset: () => void;
    setData: (data: any) => void;
}
export type UseDataSectionReturn<T> = DataSectionState<T> & DataSectionActions;
/**
 * Custom hook for managing data sections with loading, error, and empty states
 *
 * @param config - Configuration object for the data section
 * @returns Object with data state and actions
 *
 * @example
 * ```typescript
 * const tradesSection = useDataSection({
 *   fetchData: () => tradeApi.getTrades(),
 *   fetchOnMount: true,
 *   refreshInterval: 30000, // 30 seconds
 *   isEmpty: (trades) => trades.length === 0,
 *   dependencies: [filters], // Refetch when filters change
 * });
 *
 * if (tradesSection.isLoading) return <LoadingSpinner />;
 * if (tradesSection.error) return <ErrorMessage error={tradesSection.error} />;
 * if (tradesSection.isEmpty) return <EmptyState />;
 *
 * return <TradesList trades={tradesSection.data} />;
 * ```
 */
export declare const useDataSection: <T>(config: DataSectionConfig<T>) => UseDataSectionReturn<T>;
export default useDataSection;
//# sourceMappingURL=useDataSection.d.ts.map