/**
 * useFormField Hook
 *
 * EXTRACTED FROM: Multiple form components with repetitive field logic
 * Standardizes form field behavior, validation, and state management.
 *
 * BENEFITS:
 * - Consistent form field behavior
 * - Built-in validation patterns
 * - Automatic error state management
 * - Type-safe field values
 * - Reduced boilerplate in form components
 */
export type ValidationRule<T = any> = {
    validate: (value: T) => boolean;
    message: string;
};
export type FormFieldType = 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search';
export interface FormFieldConfig<T = any> {
    initialValue?: T;
    required?: boolean;
    type?: FormFieldType;
    validationRules?: ValidationRule<T>[];
    validateOnChange?: boolean;
    validateOnBlur?: boolean;
    transform?: (value: any) => T;
}
export interface FormFieldState<T = any> {
    value: T;
    error: string | null;
    touched: boolean;
    dirty: boolean;
    valid: boolean;
    validating: boolean;
}
export interface FormFieldActions<T = any> {
    setValue: (value: T) => void;
    setError: (error: string | null) => void;
    setTouched: (touched: boolean) => void;
    validate: () => Promise<boolean>;
    reset: () => void;
    handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
    handleBlur: (e: React.FocusEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
}
export type UseFormFieldReturn<T = any> = FormFieldState<T> & FormFieldActions<T>;
/**
 * Built-in validation rules for common patterns
 */
export declare const validationRules: {
    required: <T>(message?: string) => ValidationRule<T>;
    email: (message?: string) => ValidationRule<string>;
    minLength: (min: number, message?: string) => ValidationRule<string>;
    maxLength: (max: number, message?: string) => ValidationRule<string>;
    min: (min: number, message?: string) => ValidationRule<number>;
    max: (max: number, message?: string) => ValidationRule<number>;
    pattern: (regex: RegExp, message: string) => ValidationRule<string>;
};
/**
 * Custom hook for managing form field state and validation
 *
 * @param config - Configuration object for the form field
 * @returns Object with field state and actions
 *
 * @example
 * ```typescript
 * const emailField = useFormField({
 *   initialValue: '',
 *   required: true,
 *   type: 'email',
 *   validationRules: [
 *     validationRules.required(),
 *     validationRules.email(),
 *   ],
 *   validateOnBlur: true,
 * });
 *
 * return (
 *   <input
 *     type="email"
 *     value={emailField.value}
 *     onChange={emailField.handleChange}
 *     onBlur={emailField.handleBlur}
 *   />
 * );
 * ```
 */
export declare const useFormField: <T = string>(config?: FormFieldConfig<T>) => UseFormFieldReturn<T>;
export default useFormField;
//# sourceMappingURL=useFormField.d.ts.map