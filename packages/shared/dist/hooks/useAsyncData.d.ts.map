{"version": 3, "file": "useAsyncData.d.ts", "sourceRoot": "", "sources": ["../../src/hooks/useAsyncData.ts"], "names": [], "mappings": "AAOA,MAAM,WAAW,cAAc,CAAC,CAAC;IAC/B,gDAAgD;IAChD,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC;IACf,sDAAsD;IACtD,SAAS,EAAE,OAAO,CAAC;IACnB,wDAAwD;IACxD,KAAK,EAAE,KAAK,GAAG,IAAI,CAAC;IACpB,+DAA+D;IAC/D,aAAa,EAAE,OAAO,CAAC;CACxB;AAED,MAAM,WAAW,mBAAmB;IAClC,iDAAiD;IACjD,YAAY,CAAC,EAAE,OAAO,CAAC;IACvB,2DAA2D;IAC3D,YAAY,CAAC,EAAE,GAAG,EAAE,CAAC;CACtB;AAED;;;;;;GAMG;AACH,wBAAgB,YAAY,CAAC,CAAC,EAAE,CAAC,SAAS,GAAG,EAAE,GAAG,EAAE,EAClD,OAAO,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,EACrC,OAAO,GAAE,mBAAwB;2BAYb,CAAC;;IAtCrB,gDAAgD;;IAEhD,sDAAsD;eAC3C,OAAO;IAClB,wDAAwD;WACjD,KAAK,GAAG,IAAI;IACnB,+DAA+D;mBAChD,OAAO;EAoEvB"}