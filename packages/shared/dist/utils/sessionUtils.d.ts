import { TradingSession, MacroPeriod, SessionType, MacroPeriodType, SessionHierarchy, SessionSelection, TimeValidationResult, TimeRange, SessionFilterOptions } from '../types/tradingSessions';
/**
 * Session Utilities Class
 */
export declare class SessionUtils {
    private static hierarchy;
    /**
     * Initialize and get the session hierarchy
     */
    static getSessionHierarchy(): SessionHierarchy;
    /**
     * Build the complete session hierarchy with overlapping macro support
     */
    private static buildHierarchy;
    /**
     * Parse time string to minutes since midnight
     */
    static timeToMinutes(timeStr: string): number;
    /**
     * Convert minutes since midnight to time string
     */
    static minutesToTime(minutes: number): string;
    /**
     * Check if a time falls within a time range
     */
    static isTimeInRange(time: string, range: TimeRange): boolean;
    /**
     * Validate a time and suggest appropriate session/macro with overlapping support
     */
    static validateTime(time: string): TimeValidationResult;
    /**
     * Get session by type
     */
    static getSession(sessionType: SessionType): TradingSession | null;
    /**
     * Get macro period by type
     */
    static getMacroPeriod(macroType: MacroPeriodType): (MacroPeriod & {
        parentSession?: SessionType;
        spansSessions?: SessionType[];
    }) | null;
    /**
     * Get all macro periods for a session
     */
    static getMacroPeriodsForSession(sessionType: SessionType): MacroPeriod[];
    /**
     * Create a session selection
     */
    static createSessionSelection(session?: SessionType, macroPeriod?: MacroPeriodType, customTimeRange?: TimeRange): SessionSelection;
    /**
     * Filter sessions and macros based on criteria
     */
    static filterSessions(options?: SessionFilterOptions): {
        sessions: TradingSession[];
        macros: (MacroPeriod & {
            parentSession?: SessionType;
            spansSessions?: SessionType[];
        })[];
    };
    /**
     * Get current active session based on current time
     */
    static getCurrentSession(): SessionSelection | null;
    /**
     * Check if two time ranges overlap
     */
    static timeRangesOverlap(range1: TimeRange, range2: TimeRange): boolean;
    /**
     * Get display options for UI dropdowns
     */
    static getDisplayOptions(): {
        sessionOptions: Array<{
            value: SessionType;
            label: string;
            group: string;
        }>;
        macroOptions: Array<{
            value: MacroPeriodType;
            label: string;
            group: string;
            parentSession: SessionType;
        }>;
    };
    /**
     * Get all overlapping macro periods for a given time
     */
    static getOverlappingMacros(time: string): Array<{
        type: MacroPeriodType;
        macro: MacroPeriod & {
            parentSession?: SessionType;
            spansSessions?: SessionType[];
        };
        isSubPeriod: boolean;
        isMultiSession: boolean;
    }>;
    /**
     * Get multi-session macros
     */
    static getMultiSessionMacros(): MacroPeriod[];
    /**
     * Check if a macro period has sub-periods
     */
    static hasSubPeriods(macroType: MacroPeriodType): boolean;
    /**
     * Get sub-periods for a macro
     */
    static getSubPeriods(macroType: MacroPeriodType): MacroPeriod[];
    /**
     * Convert legacy session string to new session selection
     */
    static convertLegacySession(legacySession: string): SessionSelection | null;
}
//# sourceMappingURL=sessionUtils.d.ts.map