import { SessionSelection, SessionType, MacroPeriodType } from '../types/tradingSessions';
/**
 * Legacy session mapping
 */
export declare const LEGACY_SESSION_MAPPING: Record<string, {
    session?: SessionType;
    macro?: MacroPeriodType;
    description: string;
}>;
/**
 * Session Migration Utilities
 */
export declare class SessionMigrationUtils {
    /**
     * Convert legacy session string to new SessionSelection
     */
    static migrateLegacySession(legacySession: string): SessionSelection | null;
    /**
     * Convert SessionSelection back to legacy format for backward compatibility
     */
    static toLegacySession(selection: SessionSelection): string;
    /**
     * Migrate database session records to new format
     */
    static migrateDatabaseSessions(dbSessions: Array<{
        id: number;
        name: string;
        start_time: string;
        end_time: string;
        description: string;
    }>): Array<{
        id: number;
        name: string;
        start_time: string;
        end_time: string;
        description: string;
        session_type?: SessionType;
        macro_type?: MacroPeriodType;
        migrated: boolean;
    }>;
    /**
     * Generate migration report
     */
    static generateMigrationReport(legacySessions: string[]): {
        total: number;
        migrated: number;
        unmigrated: string[];
        mappings: Array<{
            legacy: string;
            new: SessionSelection | null;
            status: 'success' | 'failed';
        }>;
    };
    /**
     * Get all available legacy session names for backward compatibility
     */
    static getLegacySessionNames(): string[];
    /**
     * Check if a legacy session name is valid
     */
    static isValidLegacySession(sessionName: string): boolean;
    /**
     * Get suggested new sessions for unmapped legacy sessions
     */
    static getSuggestedMappings(unmappedSessions: string[]): Array<{
        legacy: string;
        suggestions: Array<{
            session?: SessionType;
            macro?: MacroPeriodType;
            confidence: number;
            reason: string;
        }>;
    }>;
}
//# sourceMappingURL=sessionMigration.d.ts.map