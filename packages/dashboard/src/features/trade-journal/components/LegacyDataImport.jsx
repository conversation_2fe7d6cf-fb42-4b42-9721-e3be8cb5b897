import React, { useState, useCallback } from 'react';
import { tradeStorageService } from '@adhd-trading-dashboard/shared/services';

// Simple icon components to replace lucide-react
const Upload = () => <span>📤</span>;
const FileText = () => <span>📄</span>;
const CheckCircle = () => <span>✅</span>;
const XCircle = () => <span>❌</span>;
const AlertCircle = () => <span>⚠️</span>;
const Download = () => <span>💾</span>;

const CSVImportTool = ({ onImportComplete }) => {
  const [file, setFile] = useState(null);
  const [csvData, setCsvData] = useState(null);
  const [mappedData, setMappedData] = useState(null);
  const [importStatus, setImportStatus] = useState('idle'); // idle, processing, preview, imported
  const [stats, setStats] = useState(null);
  const [importMethod, setImportMethod] = useState('csv'); // csv, json, manual, paste

  // Smart column mapping - maps CSV columns to your actual database schema (TradeRecord)
  const COLUMN_MAPPINGS = {
    // Core TradeRecord fields
    date: ['date', 'trade date', 'trading date'],
    model_type: ['model type', 'trading model', 'model', 'strategy type'],
    direction: ['direction', 'side', 'position', 'long/short'],
    market: ['market', 'symbol', 'instrument', 'ticker'],

    // Price and performance fields
    entry_price: ['entry price', 'entry', 'open price', 'buy price', 'open', 'price in'],
    exit_price: ['exit price', 'exit', 'close price', 'sell price', 'close', 'price out'],
    achieved_pl: [
      'achieved p/l',
      'pnl',
      'profit loss',
      'p&l',
      'profit/loss',
      'pl',
      'profit',
      'loss',
      'net profit',
      'realized pnl',
      'total pnl',
    ],
    r_multiple: ['r-multiple', 'r multiple', 'r', 'risk reward', 'rr', 'r ratio'],
    risk_points: ['risk (points)', 'risk points', 'risk', 'stop distance', 'stop loss'],
    no_of_contracts: [
      'no. of contracts',
      'contracts',
      'quantity',
      'size',
      'position size',
      'qty',
      'amount',
      'volume',
      'shares',
    ],

    // Trade quality and outcome
    win_loss: [
      'win/loss',
      'result',
      'outcome',
      'win loss',
      'status',
      'profit/loss',
      'winner',
      'loser',
      'w/l',
    ],
    pattern_quality_rating: [
      'pattern quality rating (1-5)',
      'pattern quality',
      'quality',
      'setup quality',
    ],

    // Session and timing
    session: ['session (time block)', 'session', 'time block', 'trading session'],
    entry_time: ['entry time', 'open time', 'start time'],
    exit_time: ['exit time', 'close time', 'end time'],

    // Setup information
    setup: ['setup', 'trade setup', 'pattern'],
    primary_setup: ['primary setup', 'main setup', 'setup type'],
    secondary_setup: ['secondary setup', 'additional setup'],

    // Additional fields
    notes: ['notes', 'comments', 'remarks', 'description'],
    rd_type: ['rd type', 'reversal type', 'rd'],
    dol_target: ['dol target', 'target', 'profit target'],
    draw_on_liquidity: ['draw on liquidity', 'dol', 'liquidity'],
  };

  // Valid trading models from your schema
  const VALID_TRADING_MODELS = ['RD-Cont', 'FVG-RD', 'Combined'];

  // Valid sessions from your application
  const VALID_SESSIONS = [
    'NY Open',
    'Lunch Macro',
    'MOC',
    'London Open',
    'Asian Session',
    'Pre-Market',
    'After Hours',
    'NY AM',
    'NY PM',
  ];

  // Valid markets
  const VALID_MARKETS = ['MNQ', 'NQ', 'ES', 'MES', 'YM', 'MYM', 'RTY', 'M2K'];

  const handleFileUpload = useCallback((event) => {
    const uploadedFile = event.target.files[0];
    if (uploadedFile && uploadedFile.type === 'text/csv') {
      setFile(uploadedFile);
      setImportStatus('processing');
      parseCSV(uploadedFile);
    }
  }, []);

  // Improved CSV parser that handles quoted fields properly
  const parseCSVLine = (line) => {
    const result = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
      const char = line[i];

      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }

    result.push(current.trim());
    return result;
  };

  const parseCSV = async (file) => {
    try {
      const text = await file.text();
      const lines = text.split('\n').filter((line) => line.trim());

      if (lines.length === 0) {
        console.error('CSV file is empty');
        setImportStatus('idle');
        return;
      }

      // Parse headers with improved CSV parsing
      const headerLine = lines[0];
      const headers = parseCSVLine(headerLine).map((h) => h.trim().toLowerCase().replace(/"/g, ''));

      console.log('Parsed headers:', headers);

      // Parse data rows
      const rows = lines
        .slice(1)
        .filter((line) => line.trim())
        .map((line) => {
          const values = parseCSVLine(line);
          const row = {};
          headers.forEach((header, index) => {
            row[header] = values[index]?.trim().replace(/"/g, '') || '';
          });
          return row;
        });

      console.log('Parsed rows:', rows.length);
      console.log('Sample row:', rows[0]);

      setCsvData({ headers, rows });
      mapColumns(headers, rows);
    } catch (error) {
      console.error('CSV parsing error:', error);
      setImportStatus('idle');
    }
  };

  const mapColumns = (headers, rows) => {
    console.log('Starting column mapping...');
    console.log('Available headers:', headers);

    // Debug: Show which columns we can map
    const mappingResults = {};
    Object.entries(COLUMN_MAPPINGS).forEach(([dbField, possibleHeaders]) => {
      const matchedHeader = headers.find((h) =>
        possibleHeaders.some((ph) => h.toLowerCase().includes(ph.toLowerCase()))
      );
      mappingResults[dbField] = matchedHeader || 'NOT FOUND';
    });
    console.log('Column mapping results:', mappingResults);

    const mapped = rows
      .map((row, rowIndex) => {
        const tradeRecord = {};

        // Smart mapping logic - map to TradeRecord schema
        Object.entries(COLUMN_MAPPINGS).forEach(([dbField, possibleHeaders]) => {
          const matchedHeader = headers.find((h) =>
            possibleHeaders.some((ph) => h.toLowerCase().includes(ph.toLowerCase()))
          );

          if (matchedHeader && row[matchedHeader]) {
            let value = row[matchedHeader].trim();

            // Field-specific transformations to match TradeRecord schema
            if (dbField === 'model_type') {
              // Map to valid trading models
              const model = VALID_TRADING_MODELS.find(
                (vm) =>
                  value.toLowerCase().includes(vm.toLowerCase()) ||
                  value
                    .toLowerCase()
                    .replace(/[-\s]/g, '')
                    .includes(vm.toLowerCase().replace(/[-\s]/g, ''))
              );
              tradeRecord[dbField] = model || 'Combined'; // Default to Combined if no match
            } else if (dbField === 'direction') {
              // Standardize direction to 'Long' | 'Short'
              const lower = value.toLowerCase();
              if (lower.includes('long') || lower.includes('buy') || lower === 'l') {
                tradeRecord[dbField] = 'Long';
              } else if (lower.includes('short') || lower.includes('sell') || lower === 's') {
                tradeRecord[dbField] = 'Short';
              } else {
                tradeRecord[dbField] = 'Long'; // Default to Long
              }
            } else if (dbField === 'win_loss') {
              // Standardize win/loss to 'Win' | 'Loss'
              const lower = value.toLowerCase();
              if (lower.includes('win') || lower.includes('profit') || lower === 'w') {
                tradeRecord[dbField] = 'Win';
              } else if (lower.includes('loss') || lower.includes('lose') || lower === 'l') {
                tradeRecord[dbField] = 'Loss';
              }
            } else if (dbField === 'session') {
              // Map to valid sessions
              const session = VALID_SESSIONS.find(
                (vs) =>
                  value.toLowerCase().includes(vs.toLowerCase()) ||
                  vs.toLowerCase().includes(value.toLowerCase())
              );
              tradeRecord[dbField] = session || value; // Keep original if no match
            } else if (dbField === 'market') {
              // Map to valid markets
              const market = VALID_MARKETS.find((vm) =>
                value.toLowerCase().includes(vm.toLowerCase())
              );
              tradeRecord[dbField] = market || 'MNQ'; // Default to MNQ
            } else if (
              ['entry_price', 'exit_price', 'achieved_pl', 'r_multiple', 'risk_points'].includes(
                dbField
              )
            ) {
              // Clean up numeric fields
              const numericValue = parseFloat(value.replace(/[^-0-9.]/g, ''));
              tradeRecord[dbField] = isNaN(numericValue) ? null : numericValue;
            } else if (dbField === 'pattern_quality_rating') {
              // Ensure quality rating is between 1-5
              const rating = parseInt(value) || 3;
              tradeRecord[dbField] = Math.max(1, Math.min(5, rating));
            } else if (dbField === 'no_of_contracts') {
              // Ensure contracts is a positive number
              const contracts = parseFloat(value) || 1;
              tradeRecord[dbField] = Math.max(0.1, contracts);
            } else if (['entry_time', 'exit_time'].includes(dbField)) {
              // Handle time fields - ensure proper format
              tradeRecord[dbField] = value.includes(':') ? value : null;
            } else {
              // String fields - just clean and assign
              tradeRecord[dbField] = value;
            }
          }
        });

        // Set required defaults for TradeRecord
        if (!tradeRecord.model_type) tradeRecord.model_type = 'Combined';
        if (!tradeRecord.direction) tradeRecord.direction = 'Long';
        if (!tradeRecord.market) tradeRecord.market = 'MNQ';
        if (!tradeRecord.pattern_quality_rating) tradeRecord.pattern_quality_rating = 3;
        if (!tradeRecord.no_of_contracts) tradeRecord.no_of_contracts = 1;

        // If no date, use today's date
        if (!tradeRecord.date) {
          tradeRecord.date = new Date().toISOString().split('T')[0];
        }

        // Debug logging for first few rows
        if (rowIndex < 3) {
          console.log(`Row ${rowIndex} mapped:`, tradeRecord);
          console.log(
            `Has date: ${!!tradeRecord.date}, Has entry_price: ${!!tradeRecord.entry_price}, Has exit_price: ${!!tradeRecord.exit_price}`
          );
        }

        // More flexible validation - just need some price data
        const hasValidData =
          tradeRecord.entry_price || tradeRecord.exit_price || tradeRecord.achieved_pl;

        if (!hasValidData) {
          if (rowIndex < 3) console.log(`Row ${rowIndex} rejected: no price data`);
          return null;
        }

        return tradeRecord;
      })
      .filter(Boolean);

    setMappedData(mapped);

    // Generate comprehensive stats
    const validTrades = mapped.filter(
      (t) => t.date && t.model_type && (t.entry_price || t.exit_price)
    );
    const unmappedModels = mapped.filter(
      (t) => t.model_type && !VALID_TRADING_MODELS.includes(t.model_type)
    ).length;
    const missingPrices = mapped.filter((t) => t.date && !t.entry_price && !t.exit_price).length;
    const winningTrades = mapped.filter((t) => t.win_loss === 'Win').length;
    const losingTrades = mapped.filter((t) => t.win_loss === 'Loss').length;

    setStats({
      totalRows: rows.length,
      validTrades: validTrades.length,
      unmappedModels,
      missingPrices,
      winningTrades,
      losingTrades,
      skipped: rows.length - validTrades.length,
      winRate:
        validTrades.length > 0
          ? ((winningTrades / (winningTrades + losingTrades)) * 100).toFixed(1)
          : 0,
    });

    setImportStatus('preview');
  };

  const handleImport = async () => {
    setImportStatus('processing');

    try {
      // Convert mapped data to CompleteTradeData and save to IndexedDB
      const importPromises = mappedData.map(async (tradeRecord) => {
        // The tradeRecord is already in the correct TradeRecord format from mapping
        const cleanedTradeRecord = {
          ...tradeRecord,
          // Ensure timestamps
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };

        // Create setup data if we have setup information
        const setupData =
          tradeRecord.primary_setup || tradeRecord.secondary_setup
            ? {
                primary_setup: tradeRecord.primary_setup || null,
                secondary_setup: tradeRecord.secondary_setup || null,
                liquidity_taken: null,
                additional_fvgs: null,
                dol: tradeRecord.dol_target || null,
              }
            : null;

        // Create FVG details if we have FVG-related information
        const fvgDetails =
          tradeRecord.rd_type || tradeRecord.draw_on_liquidity
            ? {
                fvg_date: tradeRecord.date,
                rd_type: tradeRecord.rd_type || null,
                entry_version: null,
                draw_on_liquidity: tradeRecord.draw_on_liquidity || null,
              }
            : null;

        // Create analysis data with import notes
        const analysisData = {
          tradingview_link: null,
          dol_target_type: tradeRecord.dol_target || null,
          beyond_target: null,
          clustering: null,
          path_quality: null,
          idr_context: null,
          sequential_fvg_rd: null,
          dol_notes: `Imported from CSV on ${new Date().toLocaleDateString()}. Original notes: ${
            tradeRecord.notes || 'None'
          }`,
        };

        // Create complete trade data structure matching your schema
        const completeTradeData = {
          trade: cleanedTradeRecord,
          fvg_details: fvgDetails,
          setup: setupData,
          analysis: analysisData,
        };

        return tradeStorageService.saveTradeWithDetails(completeTradeData);
      });

      await Promise.all(importPromises);
      setImportStatus('imported');

      // Call completion callback after successful import
      setTimeout(() => {
        onImportComplete?.();
      }, 2000); // Give user time to see success message
    } catch (error) {
      console.error('Import failed:', error);
      setImportStatus('preview'); // Return to preview on error
      // You could add error state handling here
    }
  };

  const downloadCleanedData = () => {
    const csv = [
      // Headers
      Object.keys(mappedData[0] || {}).join(','),
      // Data rows
      ...mappedData.map((trade) =>
        Object.values(trade)
          .map((v) => `"${v}"`)
          .join(',')
      ),
    ].join('\n');

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'cleaned_trades.csv';
    a.click();
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">📊 Legacy Trade Data Import</h2>
        <p className="text-gray-600">
          Multiple ways to import your trading data into the ADHD Trading Dashboard. Choose the
          method that works best for your data format.
        </p>

        {/* Import Method Selector */}
        <div className="mt-4 flex flex-wrap gap-2">
          <button
            onClick={() => setImportMethod('csv')}
            className={`px-4 py-2 rounded-lg text-sm font-medium ${
              importMethod === 'csv'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            📄 CSV Upload
          </button>
          <button
            onClick={() => setImportMethod('paste')}
            className={`px-4 py-2 rounded-lg text-sm font-medium ${
              importMethod === 'paste'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            📋 Copy & Paste
          </button>
          <button
            onClick={() => setImportMethod('json')}
            className={`px-4 py-2 rounded-lg text-sm font-medium ${
              importMethod === 'json'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            🔧 JSON Import
          </button>
          <button
            onClick={() => setImportMethod('manual')}
            className={`px-4 py-2 rounded-lg text-sm font-medium ${
              importMethod === 'manual'
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            ✏️ Manual Entry
          </button>
        </div>

        <div className="mt-2 text-sm text-gray-500">
          <strong>Supported fields:</strong> Date, Model Type, Direction, Market, Entry/Exit Prices,
          P&L, R-Multiple, Win/Loss, Pattern Quality, Session, Contracts, Risk, Setup Details, and
          Notes
        </div>
      </div>

      {/* Import Interface */}
      {importStatus === 'idle' && (
        <div className="space-y-6">
          {/* CSV Upload Method */}
          {importMethod === 'csv' && (
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <div className="mx-auto text-4xl mb-4">
                <Upload />
              </div>
              <div className="mb-4">
                <label htmlFor="csv-upload" className="cursor-pointer">
                  <span className="text-lg font-medium text-blue-600 hover:text-blue-500">
                    Upload CSV file
                  </span>
                  <input
                    id="csv-upload"
                    type="file"
                    accept=".csv"
                    onChange={handleFileUpload}
                    className="sr-only"
                  />
                </label>
              </div>
              <p className="text-sm text-gray-500">Select your Google Sheets CSV export</p>

              {/* Debug: Test with sample data */}
              <div className="mt-4 pt-4 border-t border-gray-200">
                <button
                  onClick={() => {
                    // Create sample CSV data for testing
                    const sampleCSV = `Date,Model Type,Direction,Entry Price,Exit Price,PnL,Result
2024-01-15,Combined,Long,100.50,102.25,175,Win
2024-01-16,RD-Cont,Short,99.75,98.50,125,Win
2024-01-17,FVG-RD,Long,101.00,100.25,-75,Loss`;

                    const blob = new Blob([sampleCSV], { type: 'text/csv' });
                    const file = new File([blob], 'sample.csv', { type: 'text/csv' });
                    setFile(file);
                    setImportStatus('processing');
                    parseCSV(file);
                  }}
                  className="text-sm text-blue-600 hover:text-blue-500 underline"
                >
                  🧪 Test with sample data
                </button>
              </div>
            </div>
          )}

          {/* Copy & Paste Method */}
          {importMethod === 'paste' && (
            <PasteImportInterface
              onDataParsed={(data) => {
                setMappedData(data);
                setImportStatus('preview');
              }}
            />
          )}

          {/* JSON Import Method */}
          {importMethod === 'json' && (
            <JSONImportInterface
              onDataParsed={(data) => {
                setMappedData(data);
                setImportStatus('preview');
              }}
            />
          )}

          {/* Manual Entry Method */}
          {importMethod === 'manual' && (
            <ManualEntryInterface
              onTradeAdded={(trade) => {
                setMappedData([trade]);
                setImportStatus('preview');
              }}
            />
          )}
        </div>
      )}

      {/* Processing */}
      {importStatus === 'processing' && (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-lg text-gray-600">Processing your CSV data...</p>
        </div>
      )}

      {/* Preview */}
      {importStatus === 'preview' && stats && (
        <div className="space-y-6">
          {/* Stats */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3">Import Summary</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
              <div className="flex items-center">
                <FileText />
                <span className="ml-2">{stats.totalRows} total rows</span>
              </div>
              <div className="flex items-center">
                <CheckCircle />
                <span className="ml-2">{stats.validTrades} valid trades</span>
              </div>
              <div className="flex items-center">
                <AlertCircle />
                <span className="ml-2">{stats.unmappedModels} unknown models</span>
              </div>
              <div className="flex items-center">
                <XCircle />
                <span className="ml-2">{stats.skipped} skipped</span>
              </div>
            </div>

            {/* Additional Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm pt-2 border-t border-gray-200">
              <div className="flex items-center">
                <span className="text-green-600">✅</span>
                <span className="ml-2">{stats.winningTrades} wins</span>
              </div>
              <div className="flex items-center">
                <span className="text-red-600">❌</span>
                <span className="ml-2">{stats.losingTrades} losses</span>
              </div>
              <div className="flex items-center">
                <span className="text-blue-600">📊</span>
                <span className="ml-2">{stats.winRate}% win rate</span>
              </div>
              <div className="flex items-center">
                <span className="text-yellow-600">⚠️</span>
                <span className="ml-2">{stats.missingPrices} missing prices</span>
              </div>
            </div>
          </div>

          {/* Preview Table */}
          <div className="bg-white border rounded-lg overflow-hidden">
            <div className="px-4 py-3 bg-gray-50 border-b">
              <h3 className="text-lg font-semibold">Preview (First 5 trades)</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    {Object.keys(mappedData[0] || {}).map((key) => (
                      <th
                        key={key}
                        className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase"
                      >
                        {key}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {mappedData.slice(0, 5).map((trade, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      {Object.entries(trade).map(([field, value], i) => (
                        <td key={i} className="px-3 py-2 text-sm text-gray-900">
                          <span
                            className={
                              // Highlight validation issues
                              (field === 'model_type' && !VALID_TRADING_MODELS.includes(value)) ||
                              (field === 'market' && !VALID_MARKETS.includes(value)) ||
                              (field === 'session' && value && !VALID_SESSIONS.includes(value)) ||
                              (field === 'direction' && !['Long', 'Short'].includes(value)) ||
                              (field === 'win_loss' && value && !['Win', 'Loss'].includes(value))
                                ? 'bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs'
                                : field === 'pattern_quality_rating' && (value < 1 || value > 5)
                                ? 'bg-red-100 text-red-800 px-2 py-1 rounded text-xs'
                                : ''
                            }
                            title={
                              // Add tooltips for validation issues
                              field === 'model_type' && !VALID_TRADING_MODELS.includes(value)
                                ? `Unknown model: ${value}. Will default to 'Combined'.`
                                : field === 'market' && !VALID_MARKETS.includes(value)
                                ? `Unknown market: ${value}. Will default to 'MNQ'.`
                                : field === 'pattern_quality_rating' && (value < 1 || value > 5)
                                ? `Invalid quality rating: ${value}. Must be 1-5.`
                                : ''
                            }
                          >
                            {typeof value === 'number' && value !== null
                              ? value.toFixed(2)
                              : value || '—'}
                          </span>
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-4">
            <button
              onClick={handleImport}
              className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 font-medium"
            >
              Import {stats.validTrades} Trades
            </button>
            <button
              onClick={downloadCleanedData}
              className="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 font-medium flex items-center gap-2"
            >
              <Download />
              Download Cleaned CSV
            </button>
            <button
              onClick={() => setImportStatus('idle')}
              className="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 font-medium"
            >
              Start Over
            </button>
          </div>
        </div>
      )}

      {/* Success */}
      {importStatus === 'imported' && (
        <div className="text-center py-8">
          <div className="mx-auto text-6xl mb-4">
            <CheckCircle />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Import Complete!</h3>
          <p className="text-gray-600 mb-6">
            {stats?.validTrades} trades have been imported into your app
          </p>
          <button
            onClick={() => setImportStatus('idle')}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
          >
            Import Another File
          </button>
        </div>
      )}
    </div>
  );
};

// Copy & Paste Import Interface
const PasteImportInterface = ({ onDataParsed }) => {
  const [pasteData, setPasteData] = useState('');
  const [format, setFormat] = useState('csv'); // csv, tsv, table

  const handlePaste = () => {
    if (!pasteData.trim()) return;

    try {
      let rows;
      if (format === 'csv') {
        rows = pasteData.split('\n').map((line) => line.split(','));
      } else if (format === 'tsv') {
        rows = pasteData.split('\n').map((line) => line.split('\t'));
      } else {
        // Table format (multiple spaces/tabs)
        rows = pasteData.split('\n').map((line) => line.split(/\s{2,}|\t/));
      }

      const headers = rows[0].map((h) => h.trim().toLowerCase());
      const dataRows = rows.slice(1).filter((row) => row.some((cell) => cell.trim()));

      // Convert to the same format as CSV parser
      const mappedData = dataRows.map((row) => {
        const trade = {};
        headers.forEach((header, index) => {
          trade[header] = row[index]?.trim() || '';
        });

        // Apply basic transformations
        return {
          date: trade.date || new Date().toISOString().split('T')[0],
          model_type: trade['model type'] || trade.model || 'Combined',
          direction: trade.direction?.toLowerCase().includes('short') ? 'Short' : 'Long',
          market: trade.market || trade.symbol || 'MNQ',
          entry_price: parseFloat(trade['entry price'] || trade.entry || 0),
          exit_price: parseFloat(trade['exit price'] || trade.exit || 0),
          achieved_pl: parseFloat(trade.pnl || trade['p&l'] || trade.profit || 0),
          win_loss: trade.result?.toLowerCase().includes('win') ? 'Win' : 'Loss',
          pattern_quality_rating: 3,
          no_of_contracts: 1,
        };
      });

      onDataParsed(mappedData);
    } catch (error) {
      console.error('Paste parsing error:', error);
      alert('Error parsing pasted data. Please check the format.');
    }
  };

  return (
    <div className="border-2 border-dashed border-blue-300 rounded-lg p-6">
      <div className="mb-4">
        <h3 className="text-lg font-semibold mb-2">📋 Copy & Paste Data</h3>
        <p className="text-sm text-gray-600 mb-4">
          Copy data from Excel, Google Sheets, or any table and paste it below.
        </p>

        <div className="flex gap-2 mb-4">
          <button
            onClick={() => setFormat('csv')}
            className={`px-3 py-1 text-sm rounded ${
              format === 'csv' ? 'bg-blue-500 text-white' : 'bg-gray-200'
            }`}
          >
            CSV (commas)
          </button>
          <button
            onClick={() => setFormat('tsv')}
            className={`px-3 py-1 text-sm rounded ${
              format === 'tsv' ? 'bg-blue-500 text-white' : 'bg-gray-200'
            }`}
          >
            TSV (tabs)
          </button>
          <button
            onClick={() => setFormat('table')}
            className={`px-3 py-1 text-sm rounded ${
              format === 'table' ? 'bg-blue-500 text-white' : 'bg-gray-200'
            }`}
          >
            Table (spaces)
          </button>
        </div>
      </div>

      <textarea
        value={pasteData}
        onChange={(e) => setPasteData(e.target.value)}
        placeholder="Paste your trading data here...
Example:
Date,Model Type,Direction,Entry Price,Exit Price,PnL,Result
2024-01-15,Combined,Long,100.50,102.25,175,Win"
        className="w-full h-40 p-3 border border-gray-300 rounded-lg font-mono text-sm"
      />

      <div className="mt-4 flex gap-2">
        <button
          onClick={handlePaste}
          disabled={!pasteData.trim()}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:bg-gray-300"
        >
          Parse Data
        </button>
        <button
          onClick={() => setPasteData('')}
          className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
        >
          Clear
        </button>
      </div>
    </div>
  );
};

// JSON Import Interface
const JSONImportInterface = ({ onDataParsed }) => {
  const [jsonData, setJsonData] = useState('');

  const handleJSONImport = () => {
    if (!jsonData.trim()) return;

    try {
      const parsed = JSON.parse(jsonData);
      let trades = Array.isArray(parsed) ? parsed : [parsed];

      // Convert JSON to trade format
      const mappedData = trades.map((trade) => ({
        date: trade.date || new Date().toISOString().split('T')[0],
        model_type: trade.model_type || trade.model || 'Combined',
        direction: trade.direction || 'Long',
        market: trade.market || trade.symbol || 'MNQ',
        entry_price: parseFloat(trade.entry_price || trade.entryPrice || 0),
        exit_price: parseFloat(trade.exit_price || trade.exitPrice || 0),
        achieved_pl: parseFloat(trade.achieved_pl || trade.pnl || trade.profit || 0),
        win_loss: trade.win_loss || trade.result || 'Win',
        pattern_quality_rating: parseInt(trade.pattern_quality_rating || 3),
        no_of_contracts: parseFloat(trade.no_of_contracts || trade.quantity || 1),
        notes: trade.notes || '',
      }));

      onDataParsed(mappedData);
    } catch (error) {
      console.error('JSON parsing error:', error);
      alert('Invalid JSON format. Please check your data.');
    }
  };

  const sampleJSON = `[
  {
    "date": "2024-01-15",
    "model_type": "Combined",
    "direction": "Long",
    "market": "MNQ",
    "entry_price": 100.50,
    "exit_price": 102.25,
    "achieved_pl": 175,
    "win_loss": "Win"
  }
]`;

  return (
    <div className="border-2 border-dashed border-green-300 rounded-lg p-6">
      <div className="mb-4">
        <h3 className="text-lg font-semibold mb-2">🔧 JSON Import</h3>
        <p className="text-sm text-gray-600 mb-4">
          Import trades from JSON format. Useful for API exports or programmatic data.
        </p>
      </div>

      <textarea
        value={jsonData}
        onChange={(e) => setJsonData(e.target.value)}
        placeholder={`Paste JSON data here...

Example:
${sampleJSON}`}
        className="w-full h-48 p-3 border border-gray-300 rounded-lg font-mono text-sm"
      />

      <div className="mt-4 flex gap-2">
        <button
          onClick={handleJSONImport}
          disabled={!jsonData.trim()}
          className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:bg-gray-300"
        >
          Import JSON
        </button>
        <button
          onClick={() => setJsonData(sampleJSON)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
        >
          Load Sample
        </button>
        <button
          onClick={() => setJsonData('')}
          className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
        >
          Clear
        </button>
      </div>
    </div>
  );
};

// Manual Entry Interface
const ManualEntryInterface = ({ onTradeAdded }) => {
  const [trade, setTrade] = useState({
    date: new Date().toISOString().split('T')[0],
    model_type: 'Combined',
    direction: 'Long',
    market: 'MNQ',
    entry_price: '',
    exit_price: '',
    achieved_pl: '',
    win_loss: 'Win',
    pattern_quality_rating: 3,
    no_of_contracts: 1,
    notes: '',
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setTrade((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    // Basic validation
    if (!trade.entry_price || !trade.exit_price) {
      alert('Please enter both entry and exit prices');
      return;
    }

    const processedTrade = {
      ...trade,
      entry_price: parseFloat(trade.entry_price),
      exit_price: parseFloat(trade.exit_price),
      achieved_pl:
        parseFloat(trade.achieved_pl) ||
        (parseFloat(trade.exit_price) - parseFloat(trade.entry_price)) *
          parseFloat(trade.no_of_contracts),
      pattern_quality_rating: parseInt(trade.pattern_quality_rating),
      no_of_contracts: parseFloat(trade.no_of_contracts),
    };

    onTradeAdded(processedTrade);
  };

  return (
    <div className="border-2 border-dashed border-purple-300 rounded-lg p-6">
      <div className="mb-4">
        <h3 className="text-lg font-semibold mb-2">✏️ Manual Entry</h3>
        <p className="text-sm text-gray-600 mb-4">
          Enter a single trade manually. Good for testing or adding individual trades.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-1">Date</label>
          <input
            type="date"
            name="date"
            value={trade.date}
            onChange={handleChange}
            className="w-full p-2 border border-gray-300 rounded"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Model Type</label>
          <select
            name="model_type"
            value={trade.model_type}
            onChange={handleChange}
            className="w-full p-2 border border-gray-300 rounded"
          >
            <option value="Combined">Combined</option>
            <option value="RD-Cont">RD-Cont</option>
            <option value="FVG-RD">FVG-RD</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Direction</label>
          <select
            name="direction"
            value={trade.direction}
            onChange={handleChange}
            className="w-full p-2 border border-gray-300 rounded"
          >
            <option value="Long">Long</option>
            <option value="Short">Short</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Market</label>
          <select
            name="market"
            value={trade.market}
            onChange={handleChange}
            className="w-full p-2 border border-gray-300 rounded"
          >
            <option value="MNQ">MNQ</option>
            <option value="NQ">NQ</option>
            <option value="ES">ES</option>
            <option value="MES">MES</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Entry Price</label>
          <input
            type="number"
            step="0.01"
            name="entry_price"
            value={trade.entry_price}
            onChange={handleChange}
            className="w-full p-2 border border-gray-300 rounded"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Exit Price</label>
          <input
            type="number"
            step="0.01"
            name="exit_price"
            value={trade.exit_price}
            onChange={handleChange}
            className="w-full p-2 border border-gray-300 rounded"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">P&L ($)</label>
          <input
            type="number"
            step="0.01"
            name="achieved_pl"
            value={trade.achieved_pl}
            onChange={handleChange}
            className="w-full p-2 border border-gray-300 rounded"
            placeholder="Auto-calculated if empty"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Result</label>
          <select
            name="win_loss"
            value={trade.win_loss}
            onChange={handleChange}
            className="w-full p-2 border border-gray-300 rounded"
          >
            <option value="Win">Win</option>
            <option value="Loss">Loss</option>
          </select>
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium mb-1">Notes</label>
          <textarea
            name="notes"
            value={trade.notes}
            onChange={handleChange}
            className="w-full p-2 border border-gray-300 rounded"
            rows="2"
            placeholder="Optional trade notes..."
          />
        </div>

        <div className="md:col-span-2">
          <button
            type="submit"
            className="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700"
          >
            Add Trade
          </button>
        </div>
      </form>
    </div>
  );
};

export default CSVImportTool;
