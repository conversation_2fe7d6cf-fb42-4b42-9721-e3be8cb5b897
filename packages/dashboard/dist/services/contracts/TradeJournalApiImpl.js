/**
 * Trade Journal API Implementation
 *
 * Concrete implementation of the TradeJournalApi contract.
 * This provides the actual functionality for trade journal operations.
 */
import { tradeStorageService, } from '@adhd-trading-dashboard/shared';
export class TradeJournalApiImpl {
    constructor() {
        this.eventListeners = {};
        this.state = {
            trades: [],
            isLoading: false,
            error: null,
            selectedTrade: null,
            filters: {},
        };
    }
    /**
     * Get trade data by ID
     */
    async getTradeData(id) {
        try {
            this.setState({ isLoading: true, error: null });
            const completeTradeData = await tradeStorageService.getTradeById(id);
            this.setState({ isLoading: false });
            if (!completeTradeData) {
                return null;
            }
            // Convert CompleteTradeData to Trade format
            return this.convertToTradeFormat(completeTradeData);
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to get trade data';
            this.setState({ isLoading: false, error: errorMessage });
            return null;
        }
    }
    /**
     * Convert CompleteTradeData to Trade format
     */
    convertToTradeFormat(completeTradeData) {
        const tradeRecord = completeTradeData.trade;
        return {
            id: tradeRecord.id?.toString() || '0',
            symbol: tradeRecord.market || 'Unknown',
            date: tradeRecord.date,
            direction: tradeRecord.direction,
            size: tradeRecord.no_of_contracts || 1,
            entry: tradeRecord.entry_price || 0,
            exit: tradeRecord.exit_price || 0,
            stopLoss: 0, // Not available in TradeRecord
            takeProfit: 0, // Not available in TradeRecord
            profitLoss: tradeRecord.achieved_pl || 0,
            strategy: tradeRecord.setupComponents?.constant || tradeRecord.setup || 'Unknown',
            notes: tradeRecord.notes || '',
            tags: [], // Not available in TradeRecord
            images: [], // Not available in TradeRecord
        };
    }
    /**
     * Validate setup components
     */
    validateSetup(setup) {
        const errors = [];
        // Validate required fields
        if (!setup.constant) {
            errors.push('Constant element is required');
        }
        if (!setup.entry) {
            errors.push('Entry method is required');
        }
        // Validate combinations
        if (setup.constant && setup.variable) {
            // Add specific validation rules for setup combinations
            if (setup.constant === 'NWOG' && setup.variable === 'London-H/L') {
                // This is a valid combination
            }
            else if (setup.constant === 'Strong-FVG' && setup.variable === 'NY-H/L') {
                // This is also valid
            }
            // Add more validation rules as needed
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
    /**
     * Get recent trades
     */
    async getRecentTrades(limit = 10) {
        try {
            this.setState({ isLoading: true, error: null });
            const completeTradeDataList = await tradeStorageService.getAllTrades();
            // Sort by date and limit
            const recentCompleteTradeData = completeTradeDataList
                .sort((a, b) => new Date(b.trade.date).getTime() - new Date(a.trade.date).getTime())
                .slice(0, limit);
            // Convert to Trade format
            const recentTrades = recentCompleteTradeData.map((data) => this.convertToTradeFormat(data));
            this.setState({ isLoading: false, trades: recentTrades });
            return recentTrades;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to get recent trades';
            this.setState({ isLoading: false, error: errorMessage });
            return [];
        }
    }
    /**
     * Calculate trade metrics
     */
    calculateMetrics(trades) {
        const totalTrades = trades.length;
        if (totalTrades === 0) {
            return {
                totalTrades: 0,
                winRate: 0,
                avgPnL: 0,
                totalPnL: 0,
            };
        }
        // For Trade interface, we need to determine wins based on profitLoss > 0
        const wins = trades.filter((trade) => trade.profitLoss > 0).length;
        const winRate = (wins / totalTrades) * 100;
        const totalPnL = trades.reduce((sum, trade) => {
            return sum + trade.profitLoss;
        }, 0);
        const avgPnL = totalPnL / totalTrades;
        return {
            totalTrades,
            winRate,
            avgPnL,
            totalPnL,
        };
    }
    /**
     * Export trades data
     */
    async exportTrades(trades, format) {
        if (format === 'json') {
            return JSON.stringify(trades, null, 2);
        }
        // CSV export
        if (trades.length === 0) {
            return 'No trades to export';
        }
        const headers = [
            'Date',
            'Symbol',
            'Direction',
            'Entry Price',
            'Exit Price',
            'Size',
            'P&L',
            'Win/Loss',
            'Strategy',
            'Notes',
        ];
        const csvRows = [
            headers.join(','),
            ...trades.map((trade) => [
                trade.date,
                trade.symbol,
                trade.direction,
                trade.entry,
                trade.exit,
                trade.size,
                trade.profitLoss,
                trade.profitLoss > 0 ? 'Win' : 'Loss',
                trade.strategy,
                (trade.notes || '').replace(/,/g, ';'), // Replace commas to avoid CSV issues
            ].join(',')),
        ];
        return csvRows.join('\n');
    }
    /**
     * Event management
     */
    addEventListener(event, listener) {
        this.eventListeners[event] = listener;
    }
    removeEventListener(event) {
        delete this.eventListeners[event];
    }
    /**
     * State management
     */
    getState() {
        return { ...this.state };
    }
    setState(updates) {
        this.state = { ...this.state, ...updates };
    }
    /**
     * Emit events
     */
    emit(event, ...args) {
        const listener = this.eventListeners[event];
        if (listener) {
            // @ts-ignore - TypeScript has trouble with this pattern
            listener(...args);
        }
    }
    /**
     * Public methods to emit events (called by other parts of the app)
     */
    emitTradeCreated(trade) {
        this.emit('onTradeCreated', trade);
    }
    emitTradeUpdated(trade) {
        this.emit('onTradeUpdated', trade);
    }
    emitTradeDeleted(tradeId) {
        this.emit('onTradeDeleted', tradeId);
    }
    emitTradesFiltered(trades) {
        this.emit('onTradesFiltered', trades);
    }
}
// Export singleton instance
export const tradeJournalApi = new TradeJournalApiImpl();
//# sourceMappingURL=TradeJournalApiImpl.js.map