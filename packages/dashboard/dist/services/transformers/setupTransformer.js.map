{"version": 3, "file": "setupTransformer.js", "sourceRoot": "", "sources": ["../../../src/services/transformers/setupTransformer.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAIH,MAAM,OAAO,gBAAgB;IAC3B;;;;OAIG;IACH,MAAM,CAAC,uBAAuB,CAAC,UAA2B;QACxD,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,kCAAkC;QAClC,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;YACxB,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC;QAED,qDAAqD;QACrD,IAAI,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;YACtD,KAAK,CAAC,IAAI,CAAC,KAAK,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;QACvC,CAAC;QAED,sDAAsD;QACtD,IAAI,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;YAC1D,KAAK,CAAC,IAAI,CAAC,KAAK,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;QACzC,CAAC;QAED,0CAA0C;QAC1C,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;YACrB,KAAK,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC;QACtC,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACzB,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,uBAAuB,CAAC,WAAmB;QAChD,IAAI,CAAC,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE,CAAC;YACpD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC;YACH,wBAAwB;YACxB,MAAM,UAAU,GAAoB;gBAClC,QAAQ,EAAE,EAAE;gBACZ,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,MAAM;gBAChB,KAAK,EAAE,EAAE;aACV,CAAC;YAEF,qCAAqC;YACrC,MAAM,UAAU,GAAG,WAAW,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;YACrD,IAAI,UAAU,EAAE,CAAC;gBACf,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;gBACjC,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;YAC9D,CAAC;YAED,qCAAqC;YACrC,MAAM,aAAa,GAAG,WAAW,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;YACnE,IAAI,aAAa,EAAE,CAAC;gBAClB,UAAU,CAAC,QAAQ,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBAC9C,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;YACjE,CAAC;YAED,mCAAmC;YACnC,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;YACjE,IAAI,WAAW,EAAE,CAAC;gBAChB,UAAU,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBAC1C,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;YAC/D,CAAC;YAED,yCAAyC;YACzC,IAAI,WAAW,EAAE,CAAC;gBAChB,UAAU,CAAC,QAAQ,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC;YAC3C,CAAC;YAED,0CAA0C;YAC1C,IAAI,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;gBAC5C,OAAO,UAAU,CAAC;YACpB,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,oCAAoC,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;YACvE,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,iBAAiB,CAAC,WAAmB;QAC1C,IAAI,CAAC,WAAW,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE,CAAC;YACpD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,wCAAwC;QACxC,MAAM,QAAQ,GAAG,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAElD,oEAAoE;QACpE,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,gBAAgB,CAAC,UAA2B;QACjD,MAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;QAE7D,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,qBAAqB,CAAC;QAC/B,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,qBAAqB,CAAC,UAA2B;QACtD,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YAC7D,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,2BAA2B;QAC3B,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;QACpF,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAErB,+BAA+B;QAC/B,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QACrD,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC;QAEzB,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACzB,CAAC;IAED;;;;;;;OAOG;IACH,MAAM,CAAC,kBAAkB,CACvB,cAAuB,EACvB,YAAqB,EACrB,cAAuB,EACvB,WAAoB;QAEpB,IAAI,CAAC,cAAc,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO;YACL,QAAQ,EAAE,cAAc;YACxB,MAAM,EAAE,YAAY,IAAI,MAAM;YAC9B,QAAQ,EAAE,cAAc,IAAI,MAAM;YAClC,KAAK,EAAE,WAAW;SACnB,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,kBAAkB,CAAC,UAA2B;QAInD,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,wBAAwB;QACxB,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YACtB,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAC1C,CAAC;QAED,iDAAiD;QACjD,IAAI,UAAU,CAAC,MAAM,KAAK,EAAE,EAAE,CAAC;YAC7B,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;QAC7B,CAAC;QAED,IAAI,UAAU,CAAC,QAAQ,KAAK,EAAE,EAAE,CAAC;YAC/B,UAAU,CAAC,QAAQ,GAAG,MAAM,CAAC;QAC/B,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,aAAa;QAClB,OAAO;YACL,QAAQ,EAAE,EAAE;YACZ,MAAM,EAAE,MAAM;YACd,QAAQ,EAAE,MAAM;YAChB,KAAK,EAAE,EAAE;SACV,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,KAAK,CAAC,UAA2B;QACtC,OAAO;YACL,QAAQ,EAAE,UAAU,CAAC,QAAQ;YAC7B,MAAM,EAAE,UAAU,CAAC,MAAM,IAAI,MAAM;YACnC,QAAQ,EAAE,UAAU,CAAC,QAAQ,IAAI,MAAM;YACvC,KAAK,EAAE,UAAU,CAAC,KAAK;SACxB,CAAC;IACJ,CAAC;CACF"}