{"version": 3, "file": "TradingDashboardContext.d.ts", "sourceRoot": "", "sources": ["../../../../src/features/trading-dashboard/context/TradingDashboardContext.tsx"], "names": [], "mappings": "AAAA;;;;;;;;;;;;GAYG;AAEH,OAAO,KAAK,EAAE,EAAsC,SAAS,EAAY,MAAM,OAAO,CAAC;AACvF,OAAO,EAAE,OAAO,EAAE,MAAM,6BAA6B,CAAC;AACtD,OAAO,EACL,KAAK,EACL,iBAAiB,EACjB,cAAc,EACd,gBAAgB,EAChB,kBAAkB,EACnB,MAAM,gCAAgC,CAAC;AAGxC,MAAM,WAAW,qBAAqB;IAEpC,MAAM,EAAE,KAAK,EAAE,CAAC;IAChB,kBAAkB,EAAE,iBAAiB,EAAE,CAAC;IACxC,SAAS,EAAE,cAAc,EAAE,CAAC;IAC5B,gBAAgB,EAAE,gBAAgB,EAAE,CAAC;IACrC,kBAAkB,EAAE,kBAAkB,EAAE,CAAC;IAGzC,SAAS,EAAE,OAAO,CAAC;IAGnB,SAAS,EAAE,OAAO,CAAC;IACnB,KAAK,EAAE,MAAM,GAAG,IAAI,CAAC;IACrB,WAAW,EAAE,IAAI,GAAG,IAAI,CAAC;CAC1B;AAED,MAAM,WAAW,uBAAuB;IAEtC,YAAY,EAAE,CAAC,GAAG,EAAE,OAAO,KAAK,IAAI,CAAC;IAGrC,WAAW,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;IACjC,UAAU,EAAE,MAAM,IAAI,CAAC;IAGvB,aAAa,EAAE,MAAM,OAAO,CAAC;IAC7B,qBAAqB,EAAE,MAAM,MAAM,CAAC;CACrC;AAED,MAAM,MAAM,4BAA4B,GAAG,qBAAqB,GAAG,uBAAuB,CAAC;AAE3F,QAAA,MAAM,uBAAuB,6CAA2D,CAAC;AAEzF,MAAM,WAAW,6BAA6B;IAC5C,QAAQ,EAAE,SAAS,CAAC;IACpB,2BAA2B;IAC3B,YAAY,CAAC,EAAE,OAAO,CAAC,qBAAqB,CAAC,CAAC;IAC9C,oCAAoC;IACpC,WAAW,CAAC,EAAE,MAAM,OAAO,CAAC;QAC1B,MAAM,EAAE,KAAK,EAAE,CAAC;QAChB,kBAAkB,EAAE,iBAAiB,EAAE,CAAC;QACxC,SAAS,EAAE,cAAc,EAAE,CAAC;QAC5B,gBAAgB,EAAE,gBAAgB,EAAE,CAAC;QACrC,kBAAkB,EAAE,kBAAkB,EAAE,CAAC;KAC1C,CAAC,CAAC;CACJ;AAED;;;;;GAKG;AACH,eAAO,MAAM,wBAAwB,EAAE,KAAK,CAAC,EAAE,CAAC,6BAA6B,CAsG5E,CAAC;AAEF;;;;;;;;;;GAUG;AACH,eAAO,MAAM,0BAA0B,QAAO,4BAW7C,CAAC;AAEF;;GAEG;AAEH;;GAEG;AACH,eAAO,MAAM,uBAAuB;;wBA1Kd,OAAO,KAAK,IAAI;CA6KrC,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,uBAAuB;;;;;;;;;uBA/Kf,OAAO,CAAC,IAAI,CAAC;sBACd,IAAI;CAwMvB,CAAC;AAEF;;GAEG;AACH,eAAO,MAAM,0BAA0B;;;CAOtC,CAAC;AAEF,eAAe,uBAAuB,CAAC"}