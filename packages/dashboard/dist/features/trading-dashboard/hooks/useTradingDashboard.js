/**
 * useTradingDashboard Hook
 *
 * Custom hook for fetching and managing trading dashboard data
 */
import { useState, useEffect, useCallback } from 'react';
import { tradeStorageService } from '@adhd-trading-dashboard/shared';
import { SetupTransformer } from '../../../services/transformers/setupTransformer';
// Mock data removed - using real data from IndexedDB
/**
 * useTradingDashboard Hook
 *
 * Fetches and manages trading dashboard data
 */
export const useTradingDashboard = () => {
    const [state, setState] = useState({
        trades: [],
        performanceMetrics: [],
        chartData: [],
        setupPerformance: [],
        sessionPerformance: [],
        isLoading: true,
        error: null,
    });
    // Calculate performance metrics based on trades
    const calculateMetrics = (trades) => {
        const totalTrades = trades.length;
        const winningTrades = trades.filter((trade) => trade.win).length;
        const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;
        const totalPnl = trades.reduce((sum, trade) => sum + trade.pnl, 0);
        const avgRMultiple = totalTrades > 0 ? trades.reduce((sum, trade) => sum + trade.rMultiple, 0) / totalTrades : 0;
        return [
            { title: 'Win Rate', value: `${winRate.toFixed(1)}%` },
            { title: 'Total P&L', value: `$${totalPnl.toFixed(2)}` },
            { title: 'Avg R-Multiple', value: avgRMultiple.toFixed(2) },
            { title: 'Total Trades', value: totalTrades },
        ];
    };
    // Calculate setup performance
    const calculateSetupPerformance = (trades) => {
        const setupMap = new Map();
        // Group trades by setup
        trades.forEach((trade) => {
            const setup = trade.setup;
            if (!setupMap.has(setup)) {
                setupMap.set(setup, []);
            }
            setupMap.get(setup)?.push(trade);
        });
        // Calculate metrics for each setup
        return Array.from(setupMap.entries())
            .map(([name, setupTrades]) => {
            const totalTrades = setupTrades.length;
            const winningTrades = setupTrades.filter((trade) => trade.win).length;
            const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;
            const pnl = setupTrades.reduce((sum, trade) => sum + trade.pnl, 0);
            const avgRMultiple = totalTrades > 0
                ? setupTrades.reduce((sum, trade) => sum + trade.rMultiple, 0) / totalTrades
                : 0;
            return {
                name,
                winRate,
                avgRMultiple,
                totalTrades,
                pnl,
            };
        })
            .sort((a, b) => b.pnl - a.pnl); // Sort by P&L descending
    };
    // Calculate session performance
    const calculateSessionPerformance = (trades) => {
        const sessionMap = new Map();
        // Group trades by session
        trades.forEach((trade) => {
            const session = trade.session;
            if (!sessionMap.has(session)) {
                sessionMap.set(session, []);
            }
            sessionMap.get(session)?.push(trade);
        });
        // Calculate metrics for each session
        return Array.from(sessionMap.entries())
            .map(([name, sessionTrades]) => {
            const totalTrades = sessionTrades.length;
            const winningTrades = sessionTrades.filter((trade) => trade.win).length;
            const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;
            const pnl = sessionTrades.reduce((sum, trade) => sum + trade.pnl, 0);
            const avgRMultiple = totalTrades > 0
                ? sessionTrades.reduce((sum, trade) => sum + trade.rMultiple, 0) / totalTrades
                : 0;
            return {
                name,
                winRate,
                avgRMultiple,
                totalTrades,
                pnl,
            };
        })
            .sort((a, b) => b.pnl - a.pnl); // Sort by P&L descending
    };
    // Generate chart data from trades
    const generateChartData = (trades) => {
        const tradesByDate = new Map();
        // Group trades by date and sum P&L
        trades.forEach((trade) => {
            const date = new Date(trade.date).toLocaleDateString('en-US', {
                month: 'numeric',
                day: 'numeric',
            });
            const currentPnl = tradesByDate.get(date) || 0;
            tradesByDate.set(date, currentPnl + trade.pnl);
        });
        // Convert to chart data with cumulative P&L
        let cumulative = 0;
        return Array.from(tradesByDate.entries())
            .sort((a, b) => new Date(a[0]).getTime() - new Date(b[0]).getTime())
            .map(([date, pnl]) => {
            cumulative += pnl;
            return { date, pnl, cumulative };
        });
    };
    // Convert CompleteTradeData to dashboard Trade format
    const convertToTradeFormat = (completeTradeData) => {
        return completeTradeData.map((data) => {
            const trade = data.trade;
            // Convert setup components to display string
            let setupDisplay = 'No setup';
            if (trade.setupComponents) {
                setupDisplay = SetupTransformer.getShortDisplayString(trade.setupComponents);
            }
            else if (trade.setup) {
                setupDisplay = trade.setup;
            }
            return {
                id: trade.id?.toString() || '0',
                date: trade.date,
                model: trade.model_type || 'Unknown',
                session: trade.session || 'Unknown',
                setup: setupDisplay,
                entry: trade.entry_time || '00:00:00',
                exit: trade.exit_time || '00:00:00',
                direction: trade.direction,
                market: trade.market || 'MNQ',
                rMultiple: trade.r_multiple || 0,
                patternQuality: trade.pattern_quality_rating || 0,
                win: trade.win_loss === 'Win',
                entryPrice: trade.entry_price || 0,
                exitPrice: trade.exit_price || 0,
                risk: trade.risk_points || 0,
                pnl: trade.achieved_pl || 0,
                dolTarget: trade.dol_target || '',
                rdType: trade.rd_type || '',
                entryVersion: data.fvg_details?.entry_version || '',
                drawOnLiquidity: data.fvg_details?.draw_on_liquidity || '',
            };
        });
    };
    // Fetch dashboard data
    const fetchDashboardData = useCallback(async () => {
        setState((prev) => ({ ...prev, isLoading: true, error: null }));
        try {
            // Fetch real trades from IndexedDB
            const completeTradeData = await tradeStorageService.getAllTrades();
            // Convert to dashboard format
            const trades = convertToTradeFormat(completeTradeData);
            // Process the data
            const performanceMetrics = calculateMetrics(trades);
            const setupPerformance = calculateSetupPerformance(trades);
            const sessionPerformance = calculateSessionPerformance(trades);
            // Generate chart data from real trades
            const chartData = generateChartData(trades);
            setState({
                trades,
                performanceMetrics,
                chartData,
                setupPerformance,
                sessionPerformance,
                isLoading: false,
                error: null,
            });
        }
        catch (error) {
            console.error('Error fetching dashboard data:', error);
            setState((prev) => ({
                ...prev,
                isLoading: false,
                error: 'Failed to load dashboard data',
            }));
        }
    }, []);
    // Fetch data on initial load
    useEffect(() => {
        fetchDashboardData();
    }, [fetchDashboardData]);
    return {
        ...state,
        fetchDashboardData,
    };
};
export default useTradingDashboard;
//# sourceMappingURL=useTradingDashboard.js.map