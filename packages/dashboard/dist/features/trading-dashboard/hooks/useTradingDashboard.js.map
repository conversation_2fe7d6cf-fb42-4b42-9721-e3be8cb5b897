{"version": 3, "file": "useTradingDashboard.js", "sourceRoot": "", "sources": ["../../../../src/features/trading-dashboard/hooks/useTradingDashboard.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;AACzD,OAAO,EAAqB,mBAAmB,EAAE,MAAM,gCAAgC,CAAC;AACxF,OAAO,EAAE,gBAAgB,EAAE,MAAM,iDAAiD,CAAC;AAUnF,qDAAqD;AAErD;;;;GAIG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG,GAEjC,EAAE;IACF,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAiB;QACjD,MAAM,EAAE,EAAE;QACV,kBAAkB,EAAE,EAAE;QACtB,SAAS,EAAE,EAAE;QACb,gBAAgB,EAAE,EAAE;QACpB,kBAAkB,EAAE,EAAE;QACtB,SAAS,EAAE,IAAI;QACf,KAAK,EAAE,IAAI;KACZ,CAAC,CAAC;IAEH,gDAAgD;IAChD,MAAM,gBAAgB,GAAG,CAAC,MAAwB,EAAuB,EAAE;QACzE,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;QAClC,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;QACjE,MAAM,OAAO,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1E,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QACnE,MAAM,YAAY,GAChB,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QAE9F,OAAO;YACL,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE;YACtD,EAAE,KAAK,EAAE,WAAW,EAAE,KAAK,EAAE,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YACxD,EAAE,KAAK,EAAE,gBAAgB,EAAE,KAAK,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YAC3D,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,WAAW,EAAE;SAC9C,CAAC;IACJ,CAAC,CAAC;IAEF,8BAA8B;IAC9B,MAAM,yBAAyB,GAAG,CAAC,MAAwB,EAAsB,EAAE;QACjF,MAAM,QAAQ,GAAG,IAAI,GAAG,EAA4B,CAAC;QAErD,wBAAwB;QACxB,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACvB,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;YAC1B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzB,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YAC1B,CAAC;YACD,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,mCAAmC;QACnC,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;aAClC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,WAAW,CAAC,EAAE,EAAE;YAC3B,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC;YACvC,MAAM,aAAa,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;YACtE,MAAM,OAAO,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1E,MAAM,GAAG,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACnE,MAAM,YAAY,GAChB,WAAW,GAAG,CAAC;gBACb,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,GAAG,WAAW;gBAC5E,CAAC,CAAC,CAAC,CAAC;YAER,OAAO;gBACL,IAAI;gBACJ,OAAO;gBACP,YAAY;gBACZ,WAAW;gBACX,GAAG;aACJ,CAAC;QACJ,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,yBAAyB;IAC7D,CAAC,CAAC;IAEF,gCAAgC;IAChC,MAAM,2BAA2B,GAAG,CAAC,MAAwB,EAAwB,EAAE;QACrF,MAAM,UAAU,GAAG,IAAI,GAAG,EAA4B,CAAC;QAEvD,0BAA0B;QAC1B,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACvB,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;YAC9B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC7B,UAAU,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YAC9B,CAAC;YACD,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,qCAAqC;QACrC,OAAO,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;aACpC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,aAAa,CAAC,EAAE,EAAE;YAC7B,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,CAAC;YACzC,MAAM,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;YACxE,MAAM,OAAO,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1E,MAAM,GAAG,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACrE,MAAM,YAAY,GAChB,WAAW,GAAG,CAAC;gBACb,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,GAAG,WAAW;gBAC9E,CAAC,CAAC,CAAC,CAAC;YAER,OAAO;gBACL,IAAI;gBACJ,OAAO;gBACP,YAAY;gBACZ,WAAW;gBACX,GAAG;aACJ,CAAC;QACJ,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,yBAAyB;IAC7D,CAAC,CAAC;IAEF,kCAAkC;IAClC,MAAM,iBAAiB,GAAG,CAAC,MAAwB,EAAoB,EAAE;QACvE,MAAM,YAAY,GAAG,IAAI,GAAG,EAAkB,CAAC;QAE/C,mCAAmC;QACnC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACvB,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,OAAO,EAAE;gBAC5D,KAAK,EAAE,SAAS;gBAChB,GAAG,EAAE,SAAS;aACf,CAAC,CAAC;YACH,MAAM,UAAU,GAAG,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/C,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,4CAA4C;QAC5C,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,OAAO,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;aACtC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;aACnE,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE;YACnB,UAAU,IAAI,GAAG,CAAC;YAClB,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;IAEF,sDAAsD;IACtD,MAAM,oBAAoB,GAAG,CAAC,iBAAsC,EAAoB,EAAE;QACxF,OAAO,iBAAiB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YACpC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YAEzB,6CAA6C;YAC7C,IAAI,YAAY,GAAG,UAAU,CAAC;YAC9B,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;gBAC1B,YAAY,GAAG,gBAAgB,CAAC,qBAAqB,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;YAC/E,CAAC;iBAAM,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;gBACvB,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC;YAC7B,CAAC;YAED,OAAO;gBACL,EAAE,EAAE,KAAK,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,GAAG;gBAC/B,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,KAAK,EAAE,KAAK,CAAC,UAAU,IAAI,SAAS;gBACpC,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,SAAS;gBACnC,KAAK,EAAE,YAAY;gBACnB,KAAK,EAAE,KAAK,CAAC,UAAU,IAAI,UAAU;gBACrC,IAAI,EAAE,KAAK,CAAC,SAAS,IAAI,UAAU;gBACnC,SAAS,EAAE,KAAK,CAAC,SAAS;gBAC1B,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,KAAK;gBAC7B,SAAS,EAAE,KAAK,CAAC,UAAU,IAAI,CAAC;gBAChC,cAAc,EAAE,KAAK,CAAC,sBAAsB,IAAI,CAAC;gBACjD,GAAG,EAAE,KAAK,CAAC,QAAQ,KAAK,KAAK;gBAC7B,UAAU,EAAE,KAAK,CAAC,WAAW,IAAI,CAAC;gBAClC,SAAS,EAAE,KAAK,CAAC,UAAU,IAAI,CAAC;gBAChC,IAAI,EAAE,KAAK,CAAC,WAAW,IAAI,CAAC;gBAC5B,GAAG,EAAE,KAAK,CAAC,WAAW,IAAI,CAAC;gBAC3B,SAAS,EAAE,KAAK,CAAC,UAAU,IAAI,EAAE;gBACjC,MAAM,EAAE,KAAK,CAAC,OAAO,IAAI,EAAE;gBAC3B,YAAY,EAAE,IAAI,CAAC,WAAW,EAAE,aAAa,IAAI,EAAE;gBACnD,eAAe,EAAE,IAAI,CAAC,WAAW,EAAE,iBAAiB,IAAI,EAAE;aAC3D,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,uBAAuB;IACvB,MAAM,kBAAkB,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;QAChD,QAAQ,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAEhE,IAAI,CAAC;YACH,mCAAmC;YACnC,MAAM,iBAAiB,GAAG,MAAM,mBAAmB,CAAC,YAAY,EAAE,CAAC;YAEnE,8BAA8B;YAC9B,MAAM,MAAM,GAAG,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;YAEvD,mBAAmB;YACnB,MAAM,kBAAkB,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;YACpD,MAAM,gBAAgB,GAAG,yBAAyB,CAAC,MAAM,CAAC,CAAC;YAC3D,MAAM,kBAAkB,GAAG,2BAA2B,CAAC,MAAM,CAAC,CAAC;YAE/D,uCAAuC;YACvC,MAAM,SAAS,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAE5C,QAAQ,CAAC;gBACP,MAAM;gBACN,kBAAkB;gBAClB,SAAS;gBACT,gBAAgB;gBAChB,kBAAkB;gBAClB,SAAS,EAAE,KAAK;gBAChB,KAAK,EAAE,IAAI;aACZ,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,QAAQ,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;gBAClB,GAAG,IAAI;gBACP,SAAS,EAAE,KAAK;gBAChB,KAAK,EAAE,+BAA+B;aACvC,CAAC,CAAC,CAAC;QACN,CAAC;IACH,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,6BAA6B;IAC7B,SAAS,CAAC,GAAG,EAAE;QACb,kBAAkB,EAAE,CAAC;IACvB,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC;IAEzB,OAAO;QACL,GAAG,KAAK;QACR,kBAAkB;KACnB,CAAC;AACJ,CAAC,CAAC;AAEF,eAAe,mBAAmB,CAAC"}