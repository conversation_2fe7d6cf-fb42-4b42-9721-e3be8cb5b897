{"version": 3, "file": "dataValidation.js", "sourceRoot": "", "sources": ["../../../../src/features/trading-dashboard/utils/dataValidation.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;GAWG;AAqBH;;GAEG;AACH,MAAM,CAAC,MAAM,cAAc,GAAG;IAC5B;;OAEG;IACH,aAAa,EAAE,CAAC,KAAU,EAAE,QAAgB,CAAC,EAAoB,EAAE;QACjE,MAAM,MAAM,GAAsB,EAAE,CAAC;QACrC,MAAM,QAAQ,GAAsB,EAAE,CAAC;QAEvC,6BAA6B;QAC7B,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,CAAC,IAAI,CAAC;gBACV,KAAK,EAAE,OAAO;gBACd,KAAK,EAAE,KAAK;gBACZ,OAAO,EAAE,8CAA8C,KAAK,EAAE;gBAC9D,QAAQ,EAAE,OAAO;aAClB,CAAC,CAAC;YACH,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;QAC9C,CAAC;QAED,gBAAgB;QAChB,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC;YAChC,QAAQ,CAAC,IAAI,CAAC;gBACZ,KAAK,EAAE,IAAI;gBACX,KAAK,EAAE,KAAK,CAAC,EAAE;gBACf,OAAO,EAAE,gCAAgC,KAAK,8BAA8B;gBAC5E,QAAQ,EAAE,SAAS;aACpB,CAAC,CAAC;QACL,CAAC;QAED,kBAAkB;QAClB,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;YAChB,QAAQ,CAAC,IAAI,CAAC;gBACZ,KAAK,EAAE,MAAM;gBACb,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,OAAO,EAAE,kCAAkC,KAAK,yBAAyB;gBACzE,QAAQ,EAAE,SAAS;aACpB,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACrC,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;gBAC7B,MAAM,CAAC,IAAI,CAAC;oBACV,KAAK,EAAE,MAAM;oBACb,KAAK,EAAE,KAAK,CAAC,IAAI;oBACjB,OAAO,EAAE,wBAAwB,KAAK,CAAC,IAAI,aAAa,KAAK,EAAE;oBAC/D,QAAQ,EAAE,OAAO;iBAClB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,2BAA2B;QAC3B,MAAM,aAAa,GAAG;YACpB,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,wBAAwB;YACxB,aAAa;YACb,aAAa;SACd,CAAC;QAEF,aAAa,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC9B,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;YAC3B,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,EAAE,EAAE,CAAC;gBAC1D,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC/B,IAAI,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACpB,QAAQ,CAAC,IAAI,CAAC;wBACZ,KAAK;wBACL,KAAK;wBACL,OAAO,EAAE,6BAA6B,KAAK,KAAK,KAAK,aAAa,KAAK,EAAE;wBACzE,QAAQ,EAAE,SAAS;qBACpB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,uBAAuB;QACvB,IAAI,KAAK,CAAC,SAAS,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;YACpE,QAAQ,CAAC,IAAI,CAAC;gBACZ,KAAK,EAAE,WAAW;gBAClB,KAAK,EAAE,KAAK,CAAC,SAAS;gBACtB,OAAO,EAAE,sBAAsB,KAAK,CAAC,SAAS,aAAa,KAAK,8BAA8B;gBAC9F,QAAQ,EAAE,SAAS;aACpB,CAAC,CAAC;QACL,CAAC;QAED,sBAAsB;QACtB,IAAI,KAAK,CAAC,QAAQ,IAAI,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChE,QAAQ,CAAC,IAAI,CAAC;gBACZ,KAAK,EAAE,UAAU;gBACjB,KAAK,EAAE,KAAK,CAAC,QAAQ;gBACrB,OAAO,EAAE,2BAA2B,KAAK,CAAC,QAAQ,aAAa,KAAK,4BAA4B;gBAChG,QAAQ,EAAE,SAAS;aACpB,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;YACN,QAAQ;SACT,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,cAAc,EAAE,CAAC,MAAa,EAAoB,EAAE;QAClD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3B,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE;oBACN;wBACE,KAAK,EAAE,QAAQ;wBACf,KAAK,EAAE,MAAM;wBACb,OAAO,EAAE,6BAA6B;wBACtC,QAAQ,EAAE,OAAO;qBAClB;iBACF;gBACD,QAAQ,EAAE,EAAE;aACb,CAAC;QACJ,CAAC;QAED,MAAM,SAAS,GAAsB,EAAE,CAAC;QACxC,MAAM,WAAW,GAAsB,EAAE,CAAC;QAE1C,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YAC9B,MAAM,MAAM,GAAG,cAAc,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YAC1D,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;YACjC,WAAW,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,SAAS,CAAC,MAAM,KAAK,CAAC;YAC/B,MAAM,EAAE,SAAS;YACjB,QAAQ,EAAE,WAAW;SACtB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,yBAAyB,EAAE,CAAC,IAAW,EAAoB,EAAE;QAC3D,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACzB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE;oBACN;wBACE,KAAK,EAAE,mBAAmB;wBAC1B,KAAK,EAAE,IAAI;wBACX,OAAO,EAAE,mCAAmC;wBAC5C,QAAQ,EAAE,OAAO;qBAClB;iBACF;gBACD,QAAQ,EAAE,EAAE;aACb,CAAC;QACJ,CAAC;QAED,MAAM,SAAS,GAAsB,EAAE,CAAC;QACxC,MAAM,WAAW,GAAsB,EAAE,CAAC;QAE1C,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC3B,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACtC,SAAS,CAAC,IAAI,CAAC;oBACb,KAAK,EAAE,mBAAmB;oBAC1B,KAAK,EAAE,IAAI;oBACX,OAAO,EAAE,2CAA2C,KAAK,EAAE;oBAC3D,QAAQ,EAAE,OAAO;iBAClB,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBAChB,SAAS,CAAC,IAAI,CAAC;oBACb,KAAK,EAAE,OAAO;oBACd,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,OAAO,EAAE,sDAAsD,KAAK,EAAE;oBACtE,QAAQ,EAAE,OAAO;iBAClB,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,MAAM,WAAW,GAAG,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBACpE,SAAS,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;gBACtC,WAAW,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;YAC5C,CAAC;YAED,2BAA2B;YAC3B,IAAI,IAAI,CAAC,WAAW,IAAI,OAAO,IAAI,CAAC,WAAW,KAAK,QAAQ,EAAE,CAAC;gBAC7D,WAAW,CAAC,IAAI,CAAC;oBACf,KAAK,EAAE,aAAa;oBACpB,KAAK,EAAE,IAAI,CAAC,WAAW;oBACvB,OAAO,EAAE,0CAA0C,KAAK,EAAE;oBAC1D,QAAQ,EAAE,SAAS;iBACpB,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,SAAS,CAAC,MAAM,KAAK,CAAC;YAC/B,MAAM,EAAE,SAAS;YACjB,QAAQ,EAAE,WAAW;SACtB,CAAC;IACJ,CAAC;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,cAAc,GAAG;IAC5B;;OAEG;IACH,aAAa,EAAE,CAAC,KAAU,EAAE,QAAgB,CAAC,EAAS,EAAE;QACtD,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAE3D,OAAO;YACL,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,KAAK,CAAC,QAAQ,EAAE;YAC7C,IAAI,EAAE,cAAc,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,WAAW;YAC7D,KAAK,EAAE,KAAK,EAAE,UAAU,IAAI,SAAS;YACrC,OAAO,EAAE,KAAK,EAAE,OAAO,IAAI,SAAS;YACpC,KAAK,EAAE,KAAK,EAAE,KAAK,IAAI,UAAU;YACjC,KAAK,EAAE,cAAc,CAAC,YAAY,CAAC,KAAK,EAAE,UAAU,CAAC,IAAI,UAAU;YACnE,IAAI,EAAE,cAAc,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,IAAI,UAAU;YACjE,SAAS,EAAE,cAAc,CAAC,iBAAiB,CAAC,KAAK,EAAE,SAAS,CAAC,IAAI,MAAM;YACvE,MAAM,EAAE,KAAK,EAAE,MAAM,IAAI,KAAK;YAC9B,SAAS,EAAE,cAAc,CAAC,cAAc,CAAC,KAAK,EAAE,UAAU,CAAC,IAAI,CAAC;YAChE,cAAc,EAAE,cAAc,CAAC,cAAc,CAAC,KAAK,EAAE,sBAAsB,CAAC,IAAI,CAAC;YACjF,GAAG,EAAE,KAAK,EAAE,QAAQ,KAAK,KAAK;YAC9B,UAAU,EAAE,cAAc,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC;YAClE,SAAS,EAAE,cAAc,CAAC,cAAc,CAAC,KAAK,EAAE,UAAU,CAAC,IAAI,CAAC;YAChE,IAAI,EAAE,cAAc,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC;YAC5D,GAAG,EAAE,cAAc,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC;YAC3D,SAAS,EAAE,KAAK,EAAE,UAAU,IAAI,EAAE;YAClC,MAAM,EAAE,KAAK,EAAE,OAAO,IAAI,EAAE;YAC5B,YAAY,EAAE,KAAK,EAAE,aAAa,IAAI,EAAE;YACxC,eAAe,EAAE,KAAK,EAAE,iBAAiB,IAAI,EAAE;SAChD,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,YAAY,EAAE,CAAC,IAAS,EAAiB,EAAE;QACzC,IAAI,CAAC,IAAI;YAAE,OAAO,IAAI,CAAC;QAEvB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;YAC/B,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;gBAAE,OAAO,IAAI,CAAC;YAC1C,OAAO,OAAO,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,YAAY,EAAE,CAAC,IAAS,EAAiB,EAAE;QACzC,IAAI,CAAC,IAAI;YAAE,OAAO,IAAI,CAAC;QAEvB,mDAAmD;QACnD,MAAM,SAAS,GAAG,iDAAiD,CAAC;QACpE,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACrD,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;QAClF,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,iBAAiB,EAAE,CAAC,SAAc,EAA2B,EAAE;QAC7D,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;YAClC,MAAM,UAAU,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;YAC3C,IAAI,UAAU,KAAK,MAAM,IAAI,UAAU,KAAK,KAAK;gBAAE,OAAO,MAAM,CAAC;YACjE,IAAI,UAAU,KAAK,OAAO,IAAI,UAAU,KAAK,MAAM;gBAAE,OAAO,OAAO,CAAC;QACtE,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,cAAc,EAAE,CAAC,KAAU,EAAiB,EAAE;QAC5C,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,EAAE;YAAE,OAAO,IAAI,CAAC;QAEvE,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAC/B,OAAO,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC;IAC3C,CAAC;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,aAAa,GAAG;IAC3B;;OAEG;IACH,oBAAoB,EAAE,CAAC,MAAwB,EAAE,UAAkB,EAAE,EAAE,EAAE;QACvE,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,OAAO,CAAC,KAAK,CAAC,wBAAwB,OAAO,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,OAAO,CAAC,IAAI,CAAC,0BAA0B,OAAO,GAAG,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;QACtE,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnD,OAAO,CAAC,GAAG,CAAC,yBAAyB,OAAO,EAAE,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB,EAAE,CAAC,MAAwB,EAAU,EAAE;QACvD,IAAI,MAAM,CAAC,OAAO;YAAE,OAAO,EAAE,CAAC;QAE9B,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;QACxC,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;QAE5C,IAAI,OAAO,GAAG,+BAA+B,UAAU,SAAS,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAE9F,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YACrB,OAAO,IAAI,QAAQ,YAAY,WAAW,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAC5E,CAAC;QAED,OAAO,IAAI,2CAA2C,CAAC;QAEvD,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,eAAe;IACb,cAAc;IACd,cAAc;IACd,aAAa;CACd,CAAC"}