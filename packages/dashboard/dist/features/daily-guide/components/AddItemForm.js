import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { Button, Input, FormField } from '@adhd-trading-dashboard/shared';
const FormContainer = styled.form `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
  margin-top: ${({ theme }) => theme.spacing?.md || '12px'};
  padding: ${({ theme }) => theme.spacing?.lg || '16px'};
  background-color: ${({ theme }) => theme.colors?.surface || '#1f2937'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  border: 2px solid ${({ theme }) => theme.colors?.primary || '#dc2626'};
  position: relative;
  
  /* F1 Racing accent */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #dc2626, #b91c1c, #dc2626);
    border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'} ${({ theme }) => theme.borderRadius?.md || '6px'} 0 0;
  }
`;
const FormHeader = styled.div `
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.sm || '8px'};
  margin-bottom: ${({ theme }) => theme.spacing?.sm || '8px'};
`;
const FormTitle = styled.h4 `
  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;
const FormIcon = styled.span `
  font-size: 1.2em;
`;
const PrioritySelect = styled.select `
  padding: ${({ theme }) => theme.spacing?.sm || '8px'};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  border: 1px solid #4b5563;
  background-color: ${({ theme }) => theme.colors?.background || '#111827'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};
  width: 100%;
  cursor: pointer;
  
  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || '#dc2626'};
    box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
  }
  
  option {
    background-color: ${({ theme }) => theme.colors?.background || '#111827'};
    color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  }
`;
const FormActions = styled.div `
  display: flex;
  justify-content: flex-end;
  gap: ${({ theme }) => theme.spacing?.sm || '8px'};
  margin-top: ${({ theme }) => theme.spacing?.md || '12px'};
  padding-top: ${({ theme }) => theme.spacing?.md || '12px'};
  border-top: 1px solid #4b5563;
`;
const CancelButton = styled(Button) `
  background: transparent;
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  border: 1px solid #4b5563;
  
  &:hover {
    background: #4b5563;
    color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  }
`;
const SubmitButton = styled(Button) `
  background: ${({ theme }) => theme.colors?.primary || '#dc2626'};
  color: white;
  border: none;
  min-width: 100px;
  
  &:hover {
    background: ${({ theme }) => theme.colors?.primaryDark || '#b91c1c'};
    transform: translateY(-1px);
  }
  
  &:disabled {
    background: #4b5563;
    color: #9ca3af;
    cursor: not-allowed;
    transform: none;
  }
`;
/**
 * AddItemForm Component
 *
 * Form for adding new trading plan action items with validation
 * and F1-themed styling.
 */
export const AddItemForm = ({ newItem, setNewItem, onSubmit, onCancel, className, }) => {
    const isValid = newItem.description.trim().length > 0;
    return (_jsxs(FormContainer, { onSubmit: onSubmit, className: className, children: [_jsxs(FormHeader, { children: [_jsx(FormIcon, { children: "\u2795" }), _jsx(FormTitle, { children: "Add Action Item" })] }), _jsx(FormField, { label: "Description", children: _jsx(Input, { value: newItem.description, onChange: (value) => setNewItem({ ...newItem, description: value }), placeholder: "Enter task description (e.g., 'Review market analysis')", required: true, fullWidth: true, autoFocus: true }) }), _jsx(FormField, { label: "Priority", children: _jsxs(PrioritySelect, { value: newItem.priority, onChange: (e) => setNewItem({ ...newItem, priority: e.target.value }), children: [_jsx("option", { value: "high", children: "\uD83D\uDD34 High Priority" }), _jsx("option", { value: "medium", children: "\uD83D\uDFE1 Medium Priority" }), _jsx("option", { value: "low", children: "\uD83D\uDFE2 Low Priority" })] }) }), _jsxs(FormActions, { children: [_jsx(CancelButton, { type: "button", onClick: onCancel, size: "small", children: "Cancel" }), _jsx(SubmitButton, { type: "submit", disabled: !isValid, size: "small", children: "Add Item" })] })] }));
};
export default AddItemForm;
//# sourceMappingURL=AddItemForm.js.map