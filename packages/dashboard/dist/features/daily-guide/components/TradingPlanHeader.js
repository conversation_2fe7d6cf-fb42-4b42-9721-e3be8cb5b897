import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { Button } from '@adhd-trading-dashboard/shared';
const HeaderContainer = styled.div `
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing?.md || '12px'} 0;
  border-bottom: 1px solid #4b5563;
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
`;
const TitleSection = styled.div `
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
`;
const Title = styled.h3 `
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0;
  letter-spacing: -0.025em;
  
  /* F1 Racing aesthetic */
  text-transform: uppercase;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
`;
const Subtitle = styled.div `
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  color: #9ca3af;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
`;
const ActionsSection = styled.div `
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.sm || '8px'};
`;
const AddButton = styled(Button) `
  min-width: 120px;
  background: ${({ isActive, theme }) => isActive ? theme.colors?.primaryDark || '#b91c1c' : theme.colors?.primary || '#dc2626'};
  
  &:hover {
    background: ${({ theme }) => theme.colors?.primaryDark || '#b91c1c'};
    transform: translateY(-1px);
  }
  
  &:disabled {
    background: #4b5563;
    color: #9ca3af;
    cursor: not-allowed;
    transform: none;
  }
`;
const StatusIndicator = styled.div `
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  padding: ${({ theme }) => theme.spacing?.xs || '4px'} ${({ theme }) => theme.spacing?.sm || '8px'};
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid #22c55e;
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  font-weight: 600;
  color: #22c55e;
  text-transform: uppercase;
  letter-spacing: 0.05em;
`;
const StatusDot = styled.div `
  width: 6px;
  height: 6px;
  background: #22c55e;
  border-radius: 50%;
  animation: pulse 2s infinite;
  
  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }
`;
/**
 * TradingPlanHeader Component
 *
 * F1-themed header that provides consistent branding and actions
 * for the Trading Plan feature.
 */
export const TradingPlanHeader = ({ onAddItem, showAddForm = false, className, }) => {
    return (_jsxs(HeaderContainer, { className: className, children: [_jsxs(TitleSection, { children: [_jsx(Title, { children: "Daily Trading Plan" }), _jsx(Subtitle, { children: "Strategy & Risk Management" })] }), _jsxs(ActionsSection, { children: [_jsxs(StatusIndicator, { children: [_jsx(StatusDot, {}), "ACTIVE PLAN"] }), onAddItem && (_jsx(AddButton, { variant: "primary", size: "small", onClick: onAddItem, isActive: showAddForm, children: showAddForm ? 'Form Open' : '➕ Add Item' }))] })] }));
};
export default TradingPlanHeader;
//# sourceMappingURL=TradingPlanHeader.js.map