/**
 * Trade Form Hook
 *
 * Custom hook for managing trade form state and logic
 */
import { TradeFormData } from '@adhd-trading-dashboard/shared';
export declare const MODEL_TYPE_OPTIONS: {
    value: string;
    label: string;
}[];
export declare const SESSION_OPTIONS: {
    value: string;
    label: string;
}[];
export declare const MARKET_OPTIONS: {
    value: string;
    label: string;
}[];
export declare const ENTRY_VERSION_OPTIONS: {
    value: string;
    label: string;
}[];
export declare const PATTERN_QUALITY_OPTIONS: {
    value: string;
    label: string;
}[];
/**
 * Main hook for managing trade form state and logic
 * @param tradeId The ID of the trade to load (optional)
 */
export declare const useTradeForm: (tradeId?: string) => {
    formValues: TradeFormData;
    setFormValues: import("react").Dispatch<import("react").SetStateAction<TradeFormData>>;
    handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
    handleSubmit: (e: React.FormEvent) => Promise<void>;
    isSubmitting: boolean;
    isLoading: boolean;
    error: string;
    success: string;
    validationErrors: import("./useTradeValidation").ValidationErrors;
    isNewTrade: boolean;
    calculateProfitLoss: () => void;
};
//# sourceMappingURL=useTradeForm.d.ts.map