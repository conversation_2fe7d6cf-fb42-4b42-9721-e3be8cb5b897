import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import styled from 'styled-components';
import { TradeJournalContent } from './trade-journal';
import TradeList from './TradeList';
const EmptyState = styled.div `
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: ${({ theme }) => theme.spacing?.xl || '48px'};
  text-align: center;
  min-height: 300px;
  background: ${({ theme }) => theme.colors?.surface || '#1f2937'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  border: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
`;
const EmptyIcon = styled.div `
  font-size: 48px;
  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
  opacity: 0.7;
`;
const EmptyTitle = styled.h3 `
  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0 0 ${({ theme }) => theme.spacing?.sm || '8px'} 0;
`;
const EmptyMessage = styled.p `
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  margin: 0;
  max-width: 400px;
`;
const StatsContainer = styled.div `
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
  margin-bottom: ${({ theme }) => theme.spacing?.xl || '32px'};
`;
const StatCard = styled.div `
  background: ${({ theme }) => theme.colors?.surface || '#1f2937'};
  border: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  text-align: center;
`;
const StatValue = styled.div `
  font-size: ${({ theme }) => theme.fontSizes?.xxl || '2rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.primary || '#dc2626'};
  margin-bottom: ${({ theme }) => theme.spacing?.xs || '4px'};
`;
const StatLabel = styled.div `
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  text-transform: uppercase;
  letter-spacing: 0.025em;
`;
/**
 * All Trades Tab Content
 */
const AllTradesTabContent = ({ data, isLoading, error, showFilters, handlers }) => {
    if (error) {
        return (_jsxs(EmptyState, { children: [_jsx(EmptyIcon, { children: "\u26A0\uFE0F" }), _jsx(EmptyTitle, { children: "Error Loading Trades" }), _jsx(EmptyMessage, { children: error })] }));
    }
    if (!isLoading && data.trades.length === 0) {
        return (_jsxs(EmptyState, { children: [_jsx(EmptyIcon, { children: "\uD83D\uDCCB" }), _jsx(EmptyTitle, { children: "No Trades Found" }), _jsx(EmptyMessage, { children: "Start building your trading journal by adding your first trade." })] }));
    }
    return (_jsx(TradeJournalContent, { error: error, showFilters: showFilters, filteredTrades: data.filteredTrades, isLoading: isLoading, filters: data.filters, handleFilterChange: handlers.handleFilterChange, resetFilters: handlers.resetFilters, uniqueSetups: data.uniqueSetups, uniqueModelTypes: data.uniqueModelTypes, uniquePrimarySetupTypes: data.uniquePrimarySetupTypes, uniqueSecondarySetupTypes: data.uniqueSecondarySetupTypes, uniqueLiquidityTypes: data.uniqueLiquidityTypes, uniqueDOLTypes: data.uniqueDOLTypes }));
};
/**
 * Recent Trades Tab Content
 */
const RecentTradesTabContent = ({ data, isLoading }) => {
    if (!isLoading && data.recentTrades.length === 0) {
        return (_jsxs(EmptyState, { children: [_jsx(EmptyIcon, { children: "\u26A1" }), _jsx(EmptyTitle, { children: "No Recent Trades" }), _jsx(EmptyMessage, { children: "Recent trades from the last 7 days will appear here." })] }));
    }
    return (_jsx(TradeList, { trades: data.recentTrades, isLoading: isLoading, title: "Recent Trades (Last 7 Days)" }));
};
/**
 * Filters Tab Content
 */
const FiltersTabContent = ({ data, isLoading, handlers }) => {
    return (_jsx(TradeJournalContent, { error: null, showFilters: true, filteredTrades: data.filteredTrades, isLoading: isLoading, filters: data.filters, handleFilterChange: handlers.handleFilterChange, resetFilters: handlers.resetFilters, uniqueSetups: data.uniqueSetups, uniqueModelTypes: data.uniqueModelTypes, uniquePrimarySetupTypes: data.uniquePrimarySetupTypes, uniqueSecondarySetupTypes: data.uniqueSecondarySetupTypes, uniqueLiquidityTypes: data.uniqueLiquidityTypes, uniqueDOLTypes: data.uniqueDOLTypes }));
};
/**
 * Statistics Tab Content
 */
const StatsTabContent = ({ data, isLoading }) => {
    if (!isLoading && data.trades.length === 0) {
        return (_jsxs(EmptyState, { children: [_jsx(EmptyIcon, { children: "\uD83D\uDCCA" }), _jsx(EmptyTitle, { children: "No Statistics Available" }), _jsx(EmptyMessage, { children: "Trade statistics will be calculated once you have recorded trades." })] }));
    }
    // Calculate basic statistics
    const totalTrades = data.trades.length;
    const winningTrades = data.trades.filter(t => t.trade.result === 'win').length;
    const winRate = totalTrades > 0 ? (winningTrades / totalTrades * 100).toFixed(1) : '0';
    const totalPnL = data.trades.reduce((sum, t) => sum + (parseFloat(t.trade.profit) || 0), 0);
    return (_jsxs("div", { children: [_jsxs(StatsContainer, { children: [_jsxs(StatCard, { children: [_jsx(StatValue, { children: totalTrades }), _jsx(StatLabel, { children: "Total Trades" })] }), _jsxs(StatCard, { children: [_jsxs(StatValue, { children: [winRate, "%"] }), _jsx(StatLabel, { children: "Win Rate" })] }), _jsxs(StatCard, { children: [_jsxs(StatValue, { children: ["$", totalPnL.toFixed(2)] }), _jsx(StatLabel, { children: "Total P&L" })] }), _jsxs(StatCard, { children: [_jsx(StatValue, { children: data.uniqueSetups.length }), _jsx(StatLabel, { children: "Unique Setups" })] })] }), _jsx(TradeList, { trades: data.trades, isLoading: isLoading, title: "All Trades with Statistics" })] }));
};
/**
 * Tab configuration with components and metadata
 */
export const JOURNAL_TAB_CONFIG = {
    all: {
        id: 'all',
        title: 'All Trades',
        description: 'Complete trade history and journal entries',
        icon: '📋',
        component: AllTradesTabContent,
        showInMobile: true,
        requiresData: false,
    },
    recent: {
        id: 'recent',
        title: 'Recent Trades',
        description: 'Latest trades and recent activity',
        icon: '⚡',
        component: RecentTradesTabContent,
        showInMobile: true,
        requiresData: false,
    },
    filters: {
        id: 'filters',
        title: 'Advanced Filters',
        description: 'Advanced filtering and search options',
        icon: '🔍',
        component: FiltersTabContent,
        showInMobile: false,
        requiresData: false,
    },
    stats: {
        id: 'stats',
        title: 'Statistics',
        description: 'Performance metrics and analytics',
        icon: '📊',
        component: StatsTabContent,
        showInMobile: true,
        requiresData: true,
    },
};
/**
 * Get tab configuration by ID
 */
export const getTabConfig = (tabId) => {
    return JOURNAL_TAB_CONFIG[tabId];
};
/**
 * Get all tab configurations
 */
export const getAllTabConfigs = () => {
    return Object.values(JOURNAL_TAB_CONFIG);
};
/**
 * Get mobile-friendly tabs
 */
export const getMobileTabConfigs = () => {
    return getAllTabConfigs().filter(config => config.showInMobile);
};
/**
 * Get tabs that require data
 */
export const getDataRequiredTabConfigs = () => {
    return getAllTabConfigs().filter(config => config.requiresData);
};
/**
 * Tab Content Renderer Component
 */
export const JournalTabContentRenderer = (props) => {
    const { activeTab } = props;
    const config = getTabConfig(activeTab);
    if (!config) {
        return (_jsxs(EmptyState, { children: [_jsx(EmptyIcon, { children: "\u274C" }), _jsx(EmptyTitle, { children: "Unknown Tab" }), _jsxs(EmptyMessage, { children: ["Tab \"", activeTab, "\" not found."] })] }));
    }
    const TabComponent = config.component;
    return (_jsx("div", { id: `journal-panel-${activeTab}`, role: "tabpanel", "aria-labelledby": `journal-tab-${activeTab}`, children: _jsx(TabComponent, { ...props }) }));
};
export default JournalTabContentRenderer;
//# sourceMappingURL=journalTabConfig.js.map