/**
 * useTradeAnalysis Hook
 *
 * Custom hook for managing trade analysis data and state
 */
import { PerformanceMetrics, EquityPoint, DistributionBar } from '../types';
/**
 * useTradeAnalysis Hook
 *
 * Fetches and provides data for trade analysis charts and metrics
 */
export declare const useTradeAnalysis: () => {
    metrics: PerformanceMetrics;
    equityCurveData: EquityPoint[];
    distributionData: DistributionBar[];
    trades: import("@adhd-trading-dashboard/shared").CompleteTradeData[];
    filter: import("./tradeAnalysisState").TradeFilter;
    sort: import("./tradeAnalysisState").TradeSort;
    page: number;
    pageSize: number;
    totalPages: number;
    tradeSummary: {
        totalTrades: number;
        winningTrades: number;
        losingTrades: number;
        breakEvenTrades: number;
        totalProfit: number;
        totalFees: number;
        netProfit: number;
        winRate: number;
        averageWin: number;
        averageLoss: number;
        profitFactor: number;
        averageRMultiple: number;
    };
    isLoading: boolean;
    error: string;
    setFilter: (key: keyof import("./tradeAnalysisState").TradeFilter, value: any) => void;
    clearFilters: () => void;
    setSort: (field: string, direction: "desc" | "asc") => void;
    setPage: (page: number) => void;
    setPageSize: (pageSize: number) => void;
    fetchTrades: () => Promise<void>;
    exportTrades: () => void;
};
//# sourceMappingURL=useTradeAnalysis.d.ts.map