{"version": 3, "file": "useTradesTableData.d.ts", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/hooks/useTradesTableData.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;GAYG;AAGH,OAAO,EAAE,KAAK,EAA+B,MAAM,UAAU,CAAC;AAC9D,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,iCAAiC,CAAC;AAE3E,MAAM,WAAW,wBAAwB;IACvC,0BAA0B;IAC1B,YAAY,EAAE,KAAK,EAAE,CAAC;IACtB,yBAAyB;IACzB,SAAS,EAAE,SAAS,CAAC;IACrB,6BAA6B;IAC7B,aAAa,EAAE,aAAa,CAAC;IAC7B,4BAA4B;IAC5B,UAAU,EAAE,CAAC,KAAK,EAAE,SAAS,KAAK,IAAI,CAAC;IACvC,2BAA2B;IAC3B,UAAU,EAAE;QACV,UAAU,EAAE,CAAC,UAAU,EAAE,MAAM,KAAK,MAAM,CAAC;QAC3C,cAAc,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,MAAM,CAAC;QAC1C,aAAa,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,MAAM,CAAC;KAC1C,CAAC;IACF,qBAAqB;IACrB,QAAQ,EAAE;QACR,mBAAmB,EAAE,CAAC,SAAS,EAAE,MAAM,KAAK,MAAM,CAAC;QACnD,gBAAgB,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,MAAM,CAAC;KAC9C,CAAC;CACH;AAED;;;;;GAKG;AACH,eAAO,MAAM,kBAAkB,GAAI,QAAQ,KAAK,EAAE,KAAG,wBAmKpD,CAAC;AAEF,eAAe,kBAAkB,CAAC"}