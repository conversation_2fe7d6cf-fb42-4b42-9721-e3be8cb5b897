import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * TradesTableRow Component
 *
 * REFACTORED FROM: TradesTable.tsx (330 lines → focused components)
 * Individual table row with F1 racing theme and optimized rendering.
 *
 * BENEFITS:
 * - Focused responsibility (single row rendering)
 * - Optimized with React.memo for performance
 * - F1 racing theme with smooth animations
 * - Reusable across different table contexts
 * - Clean prop interface and type safety
 */
import React from 'react';
import styled from 'styled-components';
import { Badge, Tag } from '@adhd-trading-dashboard/shared';
const TableRow = styled.tr `
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  background: ${({ theme, $isSelected }) => $isSelected
    ? `${theme.colors?.primary || '#dc2626'}15`
    : 'transparent'};
  cursor: pointer;
  transition: all 0.2s ease;
  
  /* F1 Racing hover effect */
  &:hover {
    background: ${({ theme, $isSelected }) => $isSelected
    ? `${theme.colors?.primary || '#dc2626'}20`
    : `${theme.colors?.primary || '#dc2626'}08`};
    transform: translateY(-1px);
    box-shadow: 0 2px 4px ${({ theme }) => theme.colors?.primary || '#dc2626'}20;
  }
  
  &:active {
    transform: translateY(0);
  }
`;
const TableCell = styled.td `
  padding: ${({ theme }) => theme.spacing?.sm || '8px'};
  vertical-align: middle;
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  border-right: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'}40;
  
  &:last-child {
    border-right: none;
  }
`;
const DirectionBadge = styled(Badge) `
  text-transform: capitalize;
  font-weight: 600;
  min-width: 60px;
  justify-content: center;
`;
const StatusBadge = styled(Badge) `
  text-transform: capitalize;
  font-weight: 600;
  min-width: 70px;
  justify-content: center;
`;
const ProfitLoss = styled.span `
  color: ${({ theme, $value }) => $value > 0
    ? theme.colors?.success || '#22c55e'
    : $value < 0
        ? theme.colors?.error || '#ef4444'
        : theme.colors?.textSecondary || '#9ca3af'};
  font-weight: ${({ $value }) => $value !== 0 ? 600 : 400};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
`;
const PriceRange = styled.span `
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
`;
const TagsContainer = styled.div `
  display: flex;
  flex-wrap: wrap;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  max-width: 150px;
`;
const SymbolCell = styled(TableCell) `
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.primary || '#dc2626'};
`;
const DateCell = styled(TableCell) `
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  min-width: 140px;
`;
/**
 * TradesTableRow Component
 *
 * PATTERN: F1 Component Pattern
 * - Racing-inspired hover effects and animations
 * - Optimized rendering with React.memo
 * - Consistent F1 color scheme
 * - Accessible interaction patterns
 * - Clean separation of concerns
 */
export const TradesTableRow = React.memo(({ trade, isSelected, onClick, formatters, handlers, }) => {
    const { formatDate, formatCurrency, formatPercent } = formatters;
    const { getDirectionVariant, getStatusVariant } = handlers;
    return (_jsxs(TableRow, { "$isSelected": isSelected, onClick: onClick, role: "button", tabIndex: 0, onKeyDown: (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                onClick();
            }
        }, "aria-selected": isSelected, title: `Trade ${trade.symbol} - Click to ${isSelected ? 'deselect' : 'select'}`, children: [_jsx(DateCell, { children: formatDate(trade.entryTime) }), _jsx(SymbolCell, { children: trade.symbol }), _jsx(TableCell, { children: _jsx(DirectionBadge, { "$direction": trade.direction, variant: getDirectionVariant(trade.direction), size: "small", children: trade.direction }) }), _jsx(TableCell, { children: _jsxs(PriceRange, { children: [trade.entryPrice.toFixed(2), " \u2192 ", trade.exitPrice.toFixed(2)] }) }), _jsx(TableCell, { children: _jsx(ProfitLoss, { "$value": trade.profitLoss, children: formatCurrency(trade.profitLoss) }) }), _jsx(TableCell, { children: _jsx(ProfitLoss, { "$value": trade.profitLossPercent, children: formatPercent(trade.profitLossPercent) }) }), _jsx(TableCell, { children: _jsx(StatusBadge, { "$status": trade.status, variant: getStatusVariant(trade.status), size: "small", children: trade.status }) }), _jsx(TableCell, { children: trade.strategy || '-' }), _jsx(TableCell, { children: _jsxs(TagsContainer, { children: [trade.tags?.slice(0, 3).map((tag, index) => (_jsx(Tag, { size: "small", variant: "default", title: trade.tags?.length > 3 ? `+${trade.tags.length - 3} more tags` : undefined, children: tag }, index))), trade.tags?.length > 3 && (_jsxs(Tag, { size: "small", variant: "secondary", children: ["+", trade.tags.length - 3] }))] }) })] }));
});
// Display name for debugging
TradesTableRow.displayName = 'TradesTableRow';
export default TradesTableRow;
//# sourceMappingURL=TradesTableRow.js.map