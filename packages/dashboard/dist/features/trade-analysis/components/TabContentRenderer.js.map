{"version": 3, "file": "TabContentRenderer.js", "sourceRoot": "", "sources": ["../../../../src/features/trade-analysis/components/TabContentRenderer.tsx"], "names": [], "mappings": ";AAQA,OAAO,EAAE,QAAQ,EAAE,MAAM,gCAAgC,CAAC;AAC1D,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,wBAAwB,EAAE,MAAM,4BAA4B,CAAC;AACtE,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AAajE;;;;;GAKG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAsC,CAAC,EACpE,SAAS,EACT,IAAI,EACJ,SAAS,EACT,KAAK,GACN,EAAE,EAAE;IACH,MAAM,EAAE,eAAe,EAAE,GAAG,gBAAgB,EAAE,CAAC;IAE/C,MAAM,aAAa,GAAG,GAAG,EAAE;QACzB,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,SAAS;gBACZ,OAAO,CACL,8BACE,KAAC,QAAQ,IACP,KAAK,EAAC,qBAAqB,EAC3B,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,CAAC,CAAC,KAAK,EACjB,YAAY,EAAE,KAAK,IAAI,EAAE,EACzB,OAAO,EAAE,CAAC,IAAI,EAAE,OAAO,EACvB,YAAY,EAAC,yDAAyD,YAEtE,KAAC,kBAAkB,KAAG,GACb,EAEX,KAAC,QAAQ,IACP,KAAK,EAAC,4BAA4B,EAClC,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,CAAC,CAAC,KAAK,EACjB,YAAY,EAAE,KAAK,IAAI,EAAE,EACzB,OAAO,EAAE,CAAC,IAAI,EAAE,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAC9E,YAAY,EAAC,qEAAqE,YAElF,KAAC,oBAAoB,IAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,EAAC,aAAa,GAAG,GACxD,EAEX,KAAC,QAAQ,IACP,KAAK,EAAC,4BAA4B,EAClC,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,CAAC,CAAC,KAAK,EACjB,YAAY,EAAE,KAAK,IAAI,EAAE,EACzB,OAAO,EAAE,CAAC,IAAI,EAAE,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAC9E,YAAY,EAAC,qEAAqE,YAElF,KAAC,oBAAoB,IAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,EAAC,aAAa,GAAG,GACxD,IACV,CACJ,CAAC;YAEJ,KAAK,QAAQ;gBACX,OAAO,CACL,8BACE,KAAC,QAAQ,IACP,KAAK,EAAC,QAAQ,EACd,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,CAAC,CAAC,KAAK,EACjB,YAAY,EAAE,KAAK,IAAI,EAAE,EACzB,OAAO,EAAE,CAAC,IAAI,EAAE,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAClD,YAAY,EAAC,+CAA+C,YAE5D,KAAC,WAAW,KAAG,GACN,EAEV,eAAe,IAAI,KAAC,WAAW,KAAG,IAClC,CACJ,CAAC;YAEJ,KAAK,SAAS;gBACZ,OAAO,CACL,KAAC,QAAQ,IACP,KAAK,EAAC,uBAAuB,EAC7B,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,CAAC,CAAC,KAAK,EACjB,YAAY,EAAE,KAAK,IAAI,EAAE,EACzB,OAAO,EAAE,CAAC,IAAI,EAAE,iBAAiB,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,KAAK,CAAC,EACxE,YAAY,EAAC,gEAAgE,YAE7E,KAAC,wBAAwB,IAAC,QAAQ,EAAC,QAAQ,EAAC,KAAK,EAAC,QAAQ,GAAG,GACpD,CACZ,CAAC;YAEJ,KAAK,YAAY;gBACf,OAAO,CACL,KAAC,QAAQ,IACP,KAAK,EAAC,yBAAyB,EAC/B,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,CAAC,CAAC,KAAK,EACjB,YAAY,EAAE,KAAK,IAAI,EAAE,EACzB,OAAO,EAAE,CAAC,IAAI,EAAE,mBAAmB,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAC5E,YAAY,EAAC,kEAAkE,YAE/E,KAAC,wBAAwB,IAAC,QAAQ,EAAC,UAAU,EAAC,KAAK,EAAC,UAAU,GAAG,GACxD,CACZ,CAAC;YAEJ,KAAK,YAAY;gBACf,OAAO,CACL,8BACE,KAAC,QAAQ,IACP,KAAK,EAAC,0BAA0B,EAChC,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,CAAC,CAAC,KAAK,EACjB,YAAY,EAAE,KAAK,IAAI,EAAE,EACzB,OAAO,EAAE,CAAC,IAAI,EAAE,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAC9E,YAAY,EAAC,mEAAmE,YAEhF,KAAC,wBAAwB,IAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,EAAC,WAAW,GAAG,GAC1D,EAEX,KAAC,QAAQ,IACP,KAAK,EAAC,wBAAwB,EAC9B,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,CAAC,CAAC,KAAK,EACjB,YAAY,EAAE,KAAK,IAAI,EAAE,EACzB,OAAO,EAAE,CAAC,IAAI,EAAE,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAC1E,YAAY,EAAC,iEAAiE,YAE9E,KAAC,wBAAwB,IAAC,QAAQ,EAAC,SAAS,EAAC,KAAK,EAAC,SAAS,GAAG,GACtD,IACV,CACJ,CAAC;YAEJ,KAAK,MAAM;gBACT,OAAO,CACL,8BACE,KAAC,QAAQ,IACP,KAAK,EAAC,4BAA4B,EAClC,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,CAAC,CAAC,KAAK,EACjB,YAAY,EAAE,KAAK,IAAI,EAAE,EACzB,OAAO,EAAE,CAAC,IAAI,EAAE,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAC9E,YAAY,EAAC,qEAAqE,YAElF,KAAC,oBAAoB,IAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,EAAC,aAAa,GAAG,GACxD,EAEX,KAAC,QAAQ,IACP,KAAK,EAAC,4BAA4B,EAClC,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,CAAC,CAAC,KAAK,EACjB,YAAY,EAAE,KAAK,IAAI,EAAE,EACzB,OAAO,EAAE,CAAC,IAAI,EAAE,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAC9E,YAAY,EAAC,qEAAqE,YAElF,KAAC,oBAAoB,IAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,EAAC,aAAa,GAAG,GACxD,IACV,CACJ,CAAC;YAEJ;gBACE,OAAO,CACL,KAAC,QAAQ,IACP,KAAK,EAAC,aAAa,EACnB,QAAQ,EAAE,IAAI,EACd,YAAY,EAAE,gBAAgB,SAAS,EAAE,YAEzC,eAAK,KAAK,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,wCAC5C,SAAS,IAC7B,GACG,CACZ,CAAC;QACN,CAAC;IACH,CAAC,CAAC;IAEF,OAAO,4BAAG,aAAa,EAAE,GAAI,CAAC;AAChC,CAAC,CAAC;AAEF,eAAe,kBAAkB,CAAC"}