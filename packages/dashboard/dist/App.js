import { jsx as _jsx } from "react/jsx-runtime";
// React import handled by JSX transform
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider } from '@adhd-trading-dashboard/shared';
import { AppRoutes } from './routes';
import AppErrorBoundary from './components/AppErrorBoundary';
/**
 * Main App component for the ADHD Trading Dashboard
 * Using BrowserRouter for better URL structure
 */
function App() {
    return (_jsx(AppErrorBoundary, { children: _jsx(ThemeProvider, { initialTheme: "f1", children: _jsx(BrowserRouter, { children: _jsx(AppRoutes, {}) }) }) }));
}
export default App;
//# sourceMappingURL=App.js.map